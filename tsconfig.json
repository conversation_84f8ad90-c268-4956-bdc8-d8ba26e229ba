{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@core/*": ["./src/core/*"], "@components/*": ["./src/components/*"], "@composables/*": ["./src/composables/*"], "@stores/*": ["./src/stores/*"], "@utils/*": ["./src/utils/*"], "@types/*": ["./src/types/*"], "@assets/*": ["./src/assets/*"]}, "types": ["node", "mapbox-gl"], "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}}