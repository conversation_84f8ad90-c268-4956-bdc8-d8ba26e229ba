# MapboxGL Demo

这是一个基于 MapboxGL JS 的演示项目，实现了文章中提到的主要功能。

## 功能特性

- ✅ 基础地图初始化
- ✅ 多种地图样式切换
- ✅ 地图控件（全屏、定位、比例尺、导航、搜索）
- ✅ 地图漫游（flyTo）
- ✅ 相机旋转动画
- ✅ 3D 地形显示（可调节夸张系数）
- ✅ 天空大气层效果
- ✅ 实时坐标显示
- ✅ 自定义瓦片源支持
- ✅ 地形参数实时调节
- ✅ 响应式设计

## 文件结构

```
mapboxgl-pre-research/
├── index.html          # 主页面
├── terrain-demo.html   # 3D地形演示页面
├── app.js             # JavaScript 逻辑
├── config.js          # 配置文件
├── start-server.py    # Python 启动脚本
├── start-server.bat   # Windows 批处理启动脚本
├── package.json       # 项目配置
└── README.md          # 说明文档
```

## 使用方法

### 1. 设置 Access Token

在 `app.js` 文件中，将以下行替换为您自己的 Mapbox Access Token：

```javascript
mapboxgl.accessToken = 'your-access-token-here';
```

**获取 Access Token：**
1. 访问 [Mapbox 官网](https://www.mapbox.com/)
2. 注册账号并登录
3. 在 Dashboard 中创建新的 Access Token
4. 复制 Token 并替换到代码中

### 2. 运行项目

由于浏览器的安全策略，需要通过 HTTP 服务器运行项目：

**方法一：使用 Python（推荐）**
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

**方法二：使用 Node.js**
```bash
# 安装 http-server
npm install -g http-server

# 运行服务器
http-server -p 8000
```

**方法三：使用 Live Server（VS Code 扩展）**
- 安装 Live Server 扩展
- 右键点击 `index.html`
- 选择 "Open with Live Server"

### 3. 访问应用

打开浏览器访问：
- 主页面：`http://localhost:8000`
- 3D地形演示：`http://localhost:8000/terrain-demo.html`

## 控制面板功能

### 地图样式
- **Streets**: 标准街道地图
- **Dark**: 深色主题地图
- **Satellite**: 卫星影像地图
- **Outdoors**: 户外地图

### 快速导航
- **飞到北京**: 快速飞行到北京市中心
- **飞到上海**: 快速飞行到上海市中心

### 动画效果
- **开始/停止旋转**: 启用相机旋转动画
- **开启/关闭3D地形**: 切换3D地形显示

### 3D地形功能
- **开启/关闭3D地形**: 切换3D地形显示
- **地形夸张系数**: 调节地形的高度夸张程度（0.5-3.0）
- **视角倾斜**: 调节地图的倾斜角度（0-85°）
- **天空大气层**: 自动添加大气层效果增强3D视觉

### 自定义瓦片源
- **Mapbox 卫星 (v4)**: 使用 Mapbox v4 API 的卫星瓦片
- **OpenStreetMap**: 开源地图瓦片
- **高德卫星图**: 高德地图的卫星影像

### 其他功能
- **重置视图**: 恢复到初始视图状态

## 地图控件说明

- **🔍 搜索框**: 地理位置搜索（左上角）
- **🧭 导航控件**: 缩放和旋转控制（右上角）
- **📍 定位按钮**: 获取当前位置（右上角）
- **⛶ 全屏按钮**: 全屏显示地图（右上角）
- **📏 比例尺**: 显示地图比例（左下角）

## 坐标信息

左下角实时显示：
- 当前鼠标位置的经纬度
- 当前地图缩放级别

## 技术栈

- **MapboxGL JS v2.9.1**: 核心地图库
- **MapboxGL Geocoder**: 地理搜索插件
- **原生 JavaScript**: 无框架依赖
- **CSS3**: 响应式样式

## 注意事项

1. **Access Token**: 必须设置有效的 Mapbox Access Token
2. **网络连接**: 需要稳定的网络连接加载地图瓦片
3. **浏览器兼容性**: 支持现代浏览器（Chrome、Firefox、Safari、Edge）
4. **HTTPS**: 定位功能需要 HTTPS 环境

## 故障排除

### 地图无法加载
- 检查 Access Token 是否正确设置
- 确认网络连接正常
- 查看浏览器控制台错误信息

### 定位功能不工作
- 确保使用 HTTPS 或 localhost
- 检查浏览器位置权限设置

### 3D地形无法显示
- 确认 Access Token 有 3D 地形权限
- 检查网络连接稳定性

## 参考资料

- [Mapbox GL JS 官方文档](https://docs.mapbox.com/mapbox-gl-js/api/)
- [Mapbox GL JS 示例](https://docs.mapbox.com/mapbox-gl-js/example/)
- [Mapbox 中文站点](http://www.mapbox.cn/)

## 许可证

本项目仅用于学习和演示目的。Mapbox 服务使用需遵循其服务条款。
