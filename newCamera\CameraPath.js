import { Vec3, radians } from './Vec3.js';
import { CatmullRomSpline, CatmullRomSegment } from './CatmullRomSpline.js';
import { Track, InterpolatedChain } from './Track.js';

// 轨迹进度因子常量
export const TRACK_PROGRESS_FACTOR = 1.1;

/**
 * 相机路径生成器
 * 从GPS轨迹数据生成平滑的3D相机路径
 */
export class CameraPath {
  constructor(points = []) {
    // 初始化默认值
    this.numberOfLookAtKeys = 16;
    this.numberOfNarrowLookAtKeys = 64;
    this.numberOfCameraKeys = 12;
    this.playDuration = 40000;
    this.currentProgress = 0;
    this.scale = { x: 1, y: 1 };

    // 相机行为配置
    this.lookAtPointCenteringBehaviour = new InterpolatedChain([
      0, 0, 0, 0, 0, 0, 0, 0.1, 0.2, 0.5, 1.0
    ]);

    // 相机缓动函数
    this.cameraEasing = (t) => {
      t *= 0.92;
      return 1.2 * t * (1 - Math.pow(t, 10)) + Math.pow(t, 12);
    };

    if (points && points.length > 0) {
      this.initializeFromPoints(points);
    } else {
      this.lookAtCurve = new CatmullRomSpline([]);
      this.narrowLookAtCurve = new CatmullRomSpline([]);
      this.cameraPath = new CatmullRomSpline([]);
    }
  }

  /**
   * 从轨迹点初始化相机路径
   * @param {Array} points - 轨迹点数组
   */
  initializeFromPoints(points) {
    const bounds = Track.getBounds(points);

    this.scale = this.getPseudoCartesianScale({
      lon: bounds.lonCenter,
      lat: bounds.latCenter,
      alt: bounds.altCenter,
    });

    const processedPoints = this.timesFromDistances(points);
    this.track = new Track(processedPoints);
    this.bounds = bounds;

    // 存储原始轨迹点供外部使用
    this.trackPoints = processedPoints;
    
    this.lookAtPoints = this.track.getLookAtPoints(
      this.numberOfLookAtKeys,
      this.lookAtPointCenteringBehaviour
    );
    
    this.narrowLookAtPoints = this.track.getLookAtPoints(
      this.numberOfNarrowLookAtKeys,
      this.lookAtPointCenteringBehaviour
    );

    if (this.lookAtPoints.length > 0) {
      this.scale = this.getPseudoCartesianScale(this.lookAtPoints[0]);
      this.lookAtCurve = this.getLookAtCurve(this.lookAtPoints);
      this.narrowLookAtCurve = this.getLookAtCurve(this.narrowLookAtPoints);
    } else {
      this.lookAtCurve = new CatmullRomSpline([]);
      this.narrowLookAtCurve = new CatmullRomSpline([]);
    }

    this.cameraPath = new CatmullRomSpline([]);
  }

  /**
   * 从距离计算时间戳
   * @param {Array} points - 原始轨迹点
   * @returns {Array} 处理后的轨迹点
   */
  timesFromDistances(points) {
    if (!points || !Array.isArray(points)) {
      return [];
    }

    let distance = 0;
    let xyPrev;

    return points.map((origPoint) => {
      const point = { ...origPoint };

      const xy = this.getPseudoCartesianCoordinatesFromLatLonAlt({
        lon: point.longitude,
        lat: point.latitude,
        alt: point.altitude,
      });

      if (xyPrev) {
        const dx = xy.x - xyPrev.x;
        const dy = xy.y - xyPrev.y;
        distance += Math.sqrt(dx * dx + dy * dy);
      }

      point.timestamp = distance;
      xyPrev = xy;
      return point;
    });
  }

  /**
   * 获取伪笛卡尔坐标系缩放比例
   * @param {Object} point - 包含lat, lon, alt的点
   * @returns {Object} 缩放比例 {x, y}
   */
  getPseudoCartesianScale(point) {
    const EARTH_CIRCUMFERENCE = 40075017; // 地球周长（米）
    return {
      x: (Math.cos(radians(point.lat)) * EARTH_CIRCUMFERENCE) / 360,
      y: EARTH_CIRCUMFERENCE / 360,
    };
  }

  /**
   * 将经纬度高度转换为伪笛卡尔坐标
   * @param {Object} point - 包含lat, lon, alt的点
   * @returns {Vec3} 3D坐标
   */
  getPseudoCartesianCoordinatesFromLatLonAlt(point) {
    const x = point.lon * this.scale.x;
    const y = point.lat * this.scale.y;
    const z = point.alt;

    return new Vec3(x, y, z);
  }

  /**
   * 将伪笛卡尔坐标转换为经纬度高度
   * @param {Vec3} vec - 3D坐标
   * @returns {Object} 包含lat, lon, alt的点
   */
  getLatLonAltFromPseudoCartesianCoordinates(vec) {
    return {
      lat: vec.y / this.scale.y,
      lon: vec.x / this.scale.x,
      alt: vec.z,
    };
  }

  /**
   * 生成观察曲线
   * @param {Array} lookAtPoints - 观察点数组
   * @returns {CatmullRomSpline} 观察曲线
   */
  getLookAtCurve(lookAtPoints) {
    if (lookAtPoints.length < 2) {
      return new CatmullRomSpline([]);
    }

    const iMax = lookAtPoints.length - 1;

    // 创建首尾扩展点以确保曲线平滑
    const firstPoint = {
      lat: 2 * lookAtPoints[0].lat - lookAtPoints[1].lat,
      lon: 2 * lookAtPoints[0].lon - lookAtPoints[1].lon,
      alt: 2 * lookAtPoints[0].alt - lookAtPoints[1].alt,
      t: 2 * lookAtPoints[0].t - lookAtPoints[1].t,
    };

    const lastPoint = {
      lat: 2 * lookAtPoints[iMax].lat - lookAtPoints[iMax - 1].lat,
      lon: 2 * lookAtPoints[iMax].lon - lookAtPoints[iMax - 1].lon,
      alt: 2 * lookAtPoints[iMax].alt - lookAtPoints[iMax - 1].alt,
      t: 2 * lookAtPoints[iMax].t - lookAtPoints[iMax - 1].t,
    };

    const points = [];

    // 添加扩展的首点
    points.push(this.getPseudoCartesianCoordinatesFromLatLonAlt(firstPoint));

    // 添加所有观察点
    lookAtPoints.forEach((point) => {
      points.push(this.getPseudoCartesianCoordinatesFromLatLonAlt(point));
    });

    // 添加扩展的尾点
    points.push(this.getPseudoCartesianCoordinatesFromLatLonAlt(lastPoint));

    return new CatmullRomSpline(points);
  }

  /**
   * 计算相机位置
   * @param {Vec3} referencePoint - 参考点
   * @param {number} distance - 距离
   * @param {number} azimuth - 方位角（度）
   * @param {number} elevation - 仰角（度）
   * @returns {Vec3} 相机位置
   */
  getCameraPosition(referencePoint, distance, azimuth, elevation) {
    const azimuthRad = radians(azimuth);
    const elevationRad = radians(elevation);
    const xy = Math.cos(elevationRad);
    const dx = distance * xy * Math.sin(azimuthRad);
    const dy = distance * xy * Math.cos(azimuthRad);
    const dz = distance * Math.sin(elevationRad);

    return new Vec3(
      referencePoint.x + dx,
      referencePoint.y + dy,
      referencePoint.z + dz
    );
  }

  /**
   * 获取指定时间的观察点
   * @param {number} t - 时间参数 (0-1)
   * @returns {Vec3|null} 观察点
   */
  getLookAtPoint(t) {
    if (!this.lookAtCurve || this.lookAtCurve.segments.length === 0) {
      return null;
    }
    return this.lookAtCurve.getPoint(t);
  }

  /**
   * 获取指定时间的窄观察点
   * @param {number} t - 时间参数 (0-1)
   * @returns {Vec3|null} 窄观察点
   */
  getNarrowLookAtPoint(t) {
    if (!this.narrowLookAtCurve || this.narrowLookAtCurve.segments.length === 0) {
      return null;
    }
    return this.narrowLookAtCurve.getPoint(t);
  }
}
