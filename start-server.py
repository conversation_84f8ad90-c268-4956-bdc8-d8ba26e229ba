#!/usr/bin/env python3
"""
MapboxGL Demo 启动脚本
使用 Python 内置的 HTTP 服务器启动项目
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# 配置
PORT = 8000
HOST = 'localhost'

def main():
    # 确保在项目目录中运行
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 检查必要文件是否存在
    required_files = ['index.html', 'app.js', 'config.js']
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        sys.exit(1)
    
    print("🚀 启动 MapboxGL Demo 服务器...")
    print(f"📁 项目目录: {script_dir}")
    print(f"🌐 服务地址: http://{HOST}:{PORT}")
    print("📝 注意: 请确保在 config.js 中设置了有效的 Mapbox Access Token")
    print("⏹️  按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    # 创建 HTTP 服务器
    handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer((HOST, PORT), handler) as httpd:
            print(f"✅ 服务器已启动在 http://{HOST}:{PORT}")
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://{HOST}:{PORT}')
                print("🌐 已自动打开浏览器")
            except Exception as e:
                print(f"⚠️  无法自动打开浏览器: {e}")
                print(f"请手动访问: http://{HOST}:{PORT}")
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n⏹️  服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用，请尝试其他端口")
            print("可以修改脚本中的 PORT 变量")
        else:
            print(f"❌ 启动服务器失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
