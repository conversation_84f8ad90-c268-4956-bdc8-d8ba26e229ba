/**
 * 3D向量数学库
 * 提供基本的3D向量运算功能
 */
export class Vec3 {
  constructor(x, y, z) {
    this.x = x;
    this.y = y;
    this.z = z;
  }

  /**
   * 向量乘以标量
   * @param {number} value - 标量值
   * @returns {Vec3} 新的向量
   */
  multiply(value) {
    return new Vec3(this.x * value, this.y * value, this.z * value);
  }

  /**
   * 向量除以标量
   * @param {number} value - 标量值
   * @returns {Vec3} 新的向量
   */
  divide(value) {
    return new Vec3(this.x / value, this.y / value, this.z / value);
  }

  /**
   * 向量加法
   * @param {Vec3} vec - 另一个向量
   * @returns {Vec3} 新的向量
   */
  add(vec) {
    return new Vec3(this.x + vec.x, this.y + vec.y, this.z + vec.z);
  }

  /**
   * 向量减法
   * @param {Vec3} vec - 另一个向量
   * @returns {Vec3} 新的向量
   */
  subtract(vec) {
    return new Vec3(this.x - vec.x, this.y - vec.y, this.z - vec.z);
  }

  /**
   * 计算向量长度
   * @returns {number} 向量长度
   */
  length() {
    return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
  }

  /**
   * 向量归一化
   * @returns {Vec3} 归一化后的向量
   */
  normalize() {
    const length = this.length();
    return length === 0 ? new Vec3(0, 0, 0) : this.multiply(1 / length);
  }

  /**
   * 计算到另一个向量的距离
   * @param {Vec3} vec - 另一个向量
   * @returns {number} 距离
   */
  distance(vec) {
    const dx = vec.x - this.x;
    const dy = vec.y - this.y;
    const dz = vec.z - this.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }
}

/**
 * 角度转弧度
 * @param {number} deg - 角度
 * @returns {number} 弧度
 */
export function radians(deg) {
  return deg * (Math.PI / 180);
}

/**
 * 弧度转角度
 * @param {number} rad - 弧度
 * @returns {number} 角度
 */
export function degrees(rad) {
  return rad / (Math.PI / 180);
}
