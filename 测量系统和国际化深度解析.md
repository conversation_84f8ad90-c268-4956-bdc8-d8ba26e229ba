# 测量系统和国际化深度解析

## 概述

Suunto 地图应用的测量系统和国际化模块是支撑全球化应用的核心基础设施。测量系统负责公制/英制单位的智能切换，国际化系统支持25种语言的动态加载和翻译。这两个系统紧密协作，为全球用户提供本地化的使用体验。

## 整体架构图

```mermaid
graph TB
    subgraph "用户环境检测"
        A[浏览器语言检测<br/>navigator.language]
        B[地理位置检测<br/>Culture API]
        C[用户偏好存储<br/>localStorage]
    end
    
    subgraph "测量系统"
        D[MeasurementSystem核心逻辑]
        E[MeasurementSystemService全局Provider]
        F[智能检测算法和映射]
    end
    
    subgraph "国际化系统"
        G[i18n配置中心]
        H[Languages语言定义]
        I[translations动态加载]
        J[namespaces命名空间]
    end
    
    subgraph "翻译资源"
        K[phrases界面文本]
        L[controls控件文本]
        M[units单位文本]
        N[translations通用翻译]
    end
    
    subgraph "应用集成"
        O[Suunto格式化库<br/>专业单位转换]
        P[组件本地化<br/>useTranslation Hook]
        Q[数值格式化<br/>sttalg库集成]
    end
    
    A --> F
    B --> F
    C --> F
    F --> D
    D --> E
    G --> H
    G --> I
    G --> J
    I --> K
    I --> L
    I --> M
    I --> N
    E --> O
    G --> P
    O --> Q
```

---

## 1. 测量系统深度分析

### 核心枚举和类型定义

```typescript
export enum MeasurementSystem {
  metric = 'METRIC',      // 公制系统
  imperial = 'IMPERIAL',  // 英制系统
}

export type MeasurementSystemContextType = [
  MeasurementSystem,                                    // 当前测量系统
  (measurementSystem: MeasurementSystem) => void,      // 设置函数
];
```

**设计特点：**
- **枚举设计**：使用字符串枚举确保类型安全
- **元组类型**：Context 值采用 React Hook 风格的元组设计
- **函数式接口**：提供 getter 和 setter 的组合

### 智能检测算法

#### 基于语言的检测
```typescript
export const getMeasurementSystemByLanguage = (language: string): MeasurementSystem => {
  if (language === 'en-US') return MeasurementSystem.imperial;
  return MeasurementSystem.metric;
};
```

#### 基于国家的检测
```typescript
export const getMeasurementSystemByCountry = (country: string): MeasurementSystem => {
  if (['United States', 'Bahamas'].includes(country)) return MeasurementSystem.imperial;
  return MeasurementSystem.metric;
};
```

#### 综合检测策略
```typescript
export const getPreferredMeasurementSystem = (
  culture: Culture | null = null,
): MeasurementSystem => {
  let measurementSystem: MeasurementSystem | undefined;
  if (culture?.AutoDetectedCountry) {
    // 优先使用地理位置检测
    measurementSystem = getMeasurementSystemByCountry(culture.AutoDetectedCountry);
  } else {
    // 降级到浏览器语言检测
    const { language } = navigator;
    measurementSystem = getMeasurementSystemByLanguage(language);
  }
  return measurementSystem;
};
```

**检测优先级：**
1. **地理位置优先**：通过 IP 地址检测用户所在国家
2. **语言降级**：地理检测失败时使用浏览器语言
3. **默认公制**：除美国和巴哈马外，全球默认使用公制

### MeasurementSystemService 全局状态管理

```typescript
function MeasurementSystemService(props: MeasurementServiceProps): React.ReactElement {
  const { children } = props;
  const [measurementSystem, setMeasurementSystem] = React.useState<MeasurementSystem>(
    getPreferredMeasurementSystem(),
  );

  React.useEffect(() => {
    loadCulture().then(getPreferredMeasurementSystem).then(setMeasurementSystem).catch(noOp);
  }, []);

  const measurementSystemValue = React.useMemo<MeasurementSystemContextType>(
    () => [measurementSystem, setMeasurementSystem],
    [measurementSystem],
  );

  return (
    <MeasurementSystemContext.Provider value={measurementSystemValue}>
      {children}
    </MeasurementSystemContext.Provider>
  );
}
```

**状态管理特点：**
- **初始化策略**：先使用本地检测，再异步加载服务端数据
- **错误处理**：使用 `noOp` 确保异步错误不影响应用
- **性能优化**：使用 `useMemo` 缓存 Context 值

---

## 2. 国际化系统深度分析

### 语言配置系统

```typescript
// Languages.ts - 25种语言的完整配置
export default {
  fiFI: { language: 'fi', code: 'fi-FI', label: 'Suomi' },
  enUS: { language: 'en', code: 'en-US', label: 'English' },
  zhCHS: { language: 'zh', code: 'zh-CHS', label: '中文' },
  jaJP: { language: 'ja', code: 'ja-JP', label: '日本語' },
  // ... 21种其他语言
};
```

**语言配置特点：**
- **三层标识**：language（简码）、code（完整代码）、label（显示名称）
- **标准化命名**：遵循 ISO 639-1 和 ISO 3166-1 标准
- **本地化标签**：使用各语言的本地名称显示

### i18next 配置系统

```typescript
const backendOptions = {
  loadPath: ([ln], [ns]) => makeFileName(ns, ln),
  request: function (options, fileName, payload, callback) {
    translations(fileName)
      .then((data) => callback(null, { data, status: 200 }))
      .catch(() => callback('Error', null));
  },
  allowMultiLoading: false,
  parse: function (rawData) { return JSON.parse(rawData); },
  crossDomain: false,
  withCredentials: false,
};

i18n
  .use(Backend)                    // HTTP 后端加载器
  .use(LanguageDetector)          // 浏览器语言检测
  .use(initReactI18next)          // React 集成
  .init({
    fallbackLng: 'en',            // 默认语言
    defaultNS: namespaces.PHRASES, // 默认命名空间
    ns: [namespaces.PHRASES],     // 加载的命名空间
    debug: process.env.NODE_ENV === 'development',
    react: { useSuspense: false }, // 禁用 Suspense
    interpolation: { escapeValue: false },
    backend: backendOptions,
  });
```

**配置特点：**
- **模块化加载**：使用 Backend 插件实现动态加载
- **自动检测**：LanguageDetector 自动检测用户语言偏好
- **开发友好**：开发环境启用调试模式
- **性能优化**：禁用 Suspense 避免加载闪烁

### 动态翻译加载系统

```typescript
// translations.ts
export const makeFileName = (ns: string, ln: string): string => `./${ns}_${ln}.json`;

export default async (file: string): Promise<Record<string, string>> => {
  const files = require.context('./locales', false, /\.json$/, 'lazy');
  return files(file);
};
```

**动态加载特点：**
- **懒加载**：使用 Webpack 的 `require.context` 实现按需加载
- **文件命名规范**：`{namespace}_{language}.json` 格式
- **异步加载**：返回 Promise 支持异步操作

### 命名空间系统

```typescript
// namespaces.js
export default {
  CONTROLS: 'controls',      // 控件相关文本
  PHRASES: 'phrases',        // 界面短语
  UNITS: 'units',           // 单位相关文本
  TRANSLATIONS: 'translations', // 通用翻译
};
```

**命名空间设计：**
- **功能分离**：按功能模块分离翻译资源
- **按需加载**：只加载当前需要的命名空间
- **维护性**：便于翻译团队分工协作

---

## 3. 单位转换和格式化系统

### Suunto 专业格式化库集成

```typescript
// WorkoutMap.ts 中的格式化逻辑
const formatting = sttalg.com.suunto.sim.formatting;
const formattingOptions = new formatting.FormattingOptions(
  this.measurementSystem === MeasurementSystem.imperial
    ? formatting.MeasurementSystem.IMPERIAL
    : formatting.MeasurementSystem.METRIC,
  false,
);

// 转换数值到 SI 单位
if (typeof value == 'number') {
  value = convertOrigValueToSiValue(this.graph, value) as number;
}

// 格式化数值
const formatterStyleName = [this.graph, formatStyle].join('');
const formatResult = sttalg.com.suunto.sim.formatting.formatWithStyle(
  formatterStyleName,
  value,
  formattingOptions,
);
```

**格式化特点：**
- **专业级精度**：使用 Suunto 专业运动数据格式化库
- **双向转换**：支持原始值到 SI 单位的转换
- **样式驱动**：基于数据类型自动选择格式化样式

### 单位转换实现

```typescript
// SummaryItem.tsx 中的单位转换
export const convertOrigValueToSiValue = (
  name: string,
  value: number | string | undefined | null,
) => {
  let resultValue = value;
  switch (name) {
    case 'HeartRate':
    case 'AvgHeartrate':
    case 'MaxHeartrate': {
      const bpmValue = value;
      if (typeof bpmValue === 'number') {
        const { formatting } = sttalg.com.suunto.sim;
        const convertResult = formatting.Units.BPM.convert(bpmValue, formatting.Units.HZ);
        if (convertResult instanceof formatting.ConversionSuccess) {
          ({ value: resultValue } = convertResult);
        }
      }
      break;
    }
    case 'Energy': {
      const kcalValue = value;
      if (typeof kcalValue === 'number') {
        const { formatting } = sttalg.com.suunto.sim;
        const convertResult = formatting.Units.KCAL.convert(kcalValue, formatting.Units.J);
        if (convertResult instanceof formatting.ConversionSuccess) {
          ({ value: resultValue } = convertResult);
        }
      }
      break;
    }
  }
  return resultValue;
};
```

**转换特点：**
- **类型安全**：完整的类型检查和错误处理
- **专业单位**：支持运动专业单位（BPM、KCAL等）
- **标准化**：统一转换到 SI 国际单位制

---

## 4. 组件集成和使用模式

### 测量系统的使用

```typescript
// 在组件中使用测量系统
const [measurementSystem, setMeasurementSystem] = React.useContext(MeasurementSystemContext);

// 根据测量系统显示不同单位
const distanceUnit = measurementSystem === MeasurementSystem.metric ? 'km' : 'mi';
const speedUnit = measurementSystem === MeasurementSystem.metric ? 'km/h' : 'mph';
const elevationUnit = measurementSystem === MeasurementSystem.metric ? 'm' : 'ft';
```

### 国际化的使用

```typescript
// 在组件中使用翻译
const { t } = useTranslation([namespaces.PHRASES]);

// 翻译文本
const translatedText = t('TXT_WORKOUT_SUMMARY');

// 带参数的翻译
const translatedWithParams = t('TXT_DISTANCE_VALUE', { value: 10.5, unit: 'km' });
```

### 语言切换组件

```typescript
// LanguageSelect.tsx
function LanguageSelect(props: LanguageSelectProps): React.ReactElement {
  const { i18n } = useTranslation();
  const defaultLanguage = localStorage.getItem('i18nextLng')?.split('-')[0] || i18n.language || 'zh';

  const handleChangeLanguage = ({ target: { value } }: React.ChangeEvent<HTMLSelectElement>) =>
    value && i18n.changeLanguage(value);

  return (
    <NativeSelect
      defaultValue={i18n.language}
      value={defaultLanguage}
      onChange={handleChangeLanguage}
    >
      {Object.values(Languages).map(({ language, label }) => (
        <option key={language} value={language}>
          {label}
        </option>
      ))}
    </NativeSelect>
  );
}
```

---

## 5. 性能优化策略

### 翻译资源优化

```mermaid
flowchart TD
    subgraph "加载策略"
        A[初始加载] --> B[默认语言 + PHRASES]
        B --> C[用户交互触发]
        C --> D[按需加载其他命名空间]
        D --> E[缓存已加载资源]
    end
    
    subgraph "代码分割"
        F[Webpack require.context] --> G[动态导入]
        G --> H[按语言分包]
        H --> I[懒加载机制]
    end
    
    subgraph "缓存策略"
        J[浏览器缓存] --> K[localStorage 持久化]
        K --> L[内存缓存]
        L --> M[避免重复加载]
    end
```

### 测量系统优化

```typescript
// 使用 useMemo 缓存转换结果
const useMeasurementConverter = () => {
  const [measurementSystem] = React.useContext(MeasurementSystemContext);

  return React.useMemo(() => ({
    distance: (meters: number) => {
      if (measurementSystem === MeasurementSystem.imperial) {
        const miles = meters * 0.000621371;
        return { value: miles, unit: 'mi' };
      }
      const km = meters / 1000;
      return { value: km, unit: 'km' };
    },
    // ... 其他转换函数
  }), [measurementSystem]);
};
```

---

## 6. 扩展性设计

### 添加新语言

```typescript
// 1. 在 Languages.ts 中添加语言配置
export default {
  // ... 现有语言
  arSA: {
    language: 'ar',
    code: 'ar-SA',
    label: 'العربية',
  },
};

// 2. 添加对应的翻译文件
// locales/phrases_ar.json
// locales/controls_ar.json
// locales/units_ar.json
// locales/translations_ar.json
```

### 添加新的测量系统

```typescript
// 扩展枚举支持更多测量系统
export enum MeasurementSystem {
  metric = 'METRIC',
  imperial = 'IMPERIAL',
  chinese = 'CHINESE',    // 中国传统单位
  japanese = 'JAPANESE',  // 日本传统单位
}
```

### 添加新的命名空间

```typescript
// 在 namespaces.js 中添加新命名空间
export default {
  CONTROLS: 'controls',
  PHRASES: 'phrases',
  UNITS: 'units',
  TRANSLATIONS: 'translations',
  SPORTS: 'sports',       // 运动相关术语
  TECHNICAL: 'technical', // 技术术语
};
```

## 总结

### 技术价值
- **智能检测**：基于地理位置和语言的智能测量系统选择
- **动态加载**：按需加载翻译资源，优化性能
- **专业格式化**：集成 Suunto 专业运动数据格式化库

### 工程价值
- **模块化设计**：清晰的职责分离和接口定义
- **类型安全**：完整的 TypeScript 类型系统
- **可扩展性**：易于添加新语言和测量系统

### 用户价值
- **本地化体验**：25种语言的完整本地化支持
- **智能适配**：自动检测用户偏好，无需手动配置
- **专业精度**：运动数据的专业级格式化和单位转换

这两个系统的完美结合，为 Suunto 地图应用提供了世界级的国际化和本地化体验，是现代全球化应用开发的优秀范例。
