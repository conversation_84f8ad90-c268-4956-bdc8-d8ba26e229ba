<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>运动轨迹可视化</title>
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no">
    <link href="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css" rel="stylesheet">
    <script src="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js"></script>
    <style>
        body { margin: 0; padding: 0; }
        #map { position: absolute; top: 0; bottom: 0; width: 100%; }
        .info-box {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
            max-width: 300px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="info-box">
        <h3>🏃‍♂️ 运动轨迹</h3>
        <p><strong>轨迹点:</strong> 384 个</p>
        <p><strong>起点:</strong> 43.9035, 10.3091</p>
        <p><strong>终点:</strong> 43.9034, 10.3088</p>
        <p><strong>海拔范围:</strong> 172.4m - 276.4m</p>
    </div>

    <script>
        // 使用默认的Mapbox token或您自己的token
        mapboxgl.accessToken = 'pk.eyJ1IjoiYXNkaWdpdGFsIiwiYSI6ImNrcTloMGg5ejAwbGMyb3B0dDRncHBlYTIifQ.XxD8GnrgEGrzkc7TiRUE-A';
        
        const map = new mapboxgl.Map({
            container: 'map',
            style: 'mapbox://styles/mapbox/outdoors-v11',
            center: [10.299597599999998, 43.9072912],
            zoom: 14
        });
        
        map.on('load', () => {
            // 添加轨迹数据源
            map.addSource('track', {
                type: 'geojson',
                data: {
                    type: 'Feature',
                    properties: {},
                    geometry: {
                        type: 'LineString',
                        coordinates: [[10.3090512,43.9034624],[10.30912,43.9035808],[10.309152,43.9037312],[10.3092048,43.9037856],[10.309268,43.9038176],[10.3093752,43.90384],[10.3095576,43.9038528],[10.3096816,43.90392],[10.30982,43.9041792],[10.3098248,43.90424],[10.3098832,43.9044256],[10.310068,43.9047616],[10.3101416,43.9048192],[10.310228,43.9048192],[10.3103768,43.9046816],[10.3104216,43.9046144],[10.3104952,43.9044],[10.3105624,43.9043008],[10.3107232,43.9042976],[10.3107616,43.9043584],[10.3107632,43.904512],[10.310788,43.9046176],[10.310908,43.904784],[10.3109976,43.9049504],[10.31102,43.90496],[10.3110616,43.905152],[10.311156,43.9053184],[10.3111584,43.9053728],[10.3111136,43.9054144],[10.31096,43.9053856],[10.3108664,43.9053408],[10.3108256,43.9053376],[10.3106784,43.905408],[10.3104936,43.90544],[10.3103504,43.90544],[10.3101656,43.9054752],[10.3101056,43.9054592],[10.3098896,43.9054912],[10.3098112,43.9054752],[10.30974,43.9054848],[10.3095352,43.9054688],[10.3091736,43.9054688],[10.3090752,43.905472],[10.30894,43.9055008],[10.3087856,43.9055936],[10.308592,43.9057728],[10.30846,43.905872],[10.3082312,43.9059936],[10.3081648,43.906048],[10.3078672,43.906224],[10.3076848,43.9063072],[10.3075568,43.9064512],[10.3075464,43.906544],[10.3076016,43.9066496],[10.3077,43.9067232],[10.30784,43.9068672],[10.3078584,43.9069088],[10.30782,43.9069856],[10.3077696,43.9070432],[10.3074776,43.9072416],[10.3072816,43.9073984],[10.307252,43.9074944],[10.3071344,43.9076512],[10.3070224,43.9077344],[10.3067976,43.9078592],[10.3067528,43.9079168],[10.306764,43.9079424],[10.3068104,43.907968],[10.3070672,43.9080608],[10.3072504,43.908192],[10.3073056,43.9083168],[10.307348,43.9084704],[10.3073064,43.9085888],[10.3069536,43.9089952],[10.3068512,43.9090656],[10.3067104,43.909104],[10.3065528,43.9091232],[10.3063672,43.9091904],[10.3061912,43.9093248],[10.3057432,43.9095456],[10.3054832,43.9096384],[10.3049288,43.9097952],[10.3046616,43.9098336],[10.304552,43.9098176],[10.3044368,43.90976],[10.3041048,43.909488],[10.3039104,43.9093536],[10.3036416,43.9092736],[10.3034064,43.9092864],[10.3033088,43.909328],[10.303244,43.9093984],[10.3032464,43.9095776],[10.3032664,43.9096448],[10.3032504,43.9098144],[10.3030768,43.910048],[10.3029616,43.9101376],[10.3026104,43.9101632],[10.3024656,43.9101408],[10.3020936,43.9101216],[10.3014368,43.9100256],[10.3010928,43.9100288],[10.3010256,43.910048],[10.3009328,43.9101472],[10.3008168,43.9103456],[10.3007696,43.9106016],[10.3007272,43.910672],[10.30068,43.9107008],[10.3005504,43.9107136],[10.30046,43.910688],[10.3001736,43.9105568],[10.3000384,43.9105504],[10.2999296,43.9106016],[10.2994984,43.9108672],[10.299084,43.9110464],[10.2989816,43.911072],[10.298908,43.9110656],[10.2987736,43.9110304],[10.2986936,43.9109664],[10.2986072,43.9108704],[10.2985088,43.9106624],[10.2984616,43.9106208],[10.2983568,43.9105824],[10.2982384,43.9105696],[10.2977832,43.9105536],[10.2976512,43.9105152],[10.2974728,43.9104128],[10.2973336,43.9102816],[10.2972936,43.9101792],[10.2973048,43.910096],[10.2973736,43.9099072],[10.2973736,43.9098176],[10.2972816,43.9096832],[10.29712,43.9095296],[10.2970816,43.9095072],[10.2969536,43.9095008],[10.2967184,43.909552],[10.2965544,43.909568],[10.29634,43.90952],[10.2961528,43.9094304],[10.2957872,43.909184],[10.2956104,43.9090304],[10.2953584,43.908896],[10.295288,43.9088768],[10.2951696,43.9088768],[10.294924,43.9089536],[10.294808,43.9089632],[10.2946552,43.9089344],[10.2943384,43.9089088],[10.2933904,43.9088896],[10.2931216,43.9088256],[10.2928216,43.9086944],[10.2921136,43.908528],[10.2918656,43.9085344],[10.2915984,43.9085856],[10.2913784,43.9085728],[10.29116,43.90848],[10.291012,43.9084352],[10.290624,43.9083936],[10.2904216,43.9083456],[10.2900512,43.9081952],[10.2896632,43.9079552],[10.2895272,43.9078944],[10.2892864,43.9079008],[10.2890856,43.907968],[10.2888544,43.9080032],[10.2887064,43.9080736],[10.2880952,43.908208],[10.287996,43.9083072],[10.2879864,43.9083648],[10.288,43.9083936],[10.288056,43.9084288],[10.288192,43.9084544],[10.2885216,43.9083168],[10.2888272,43.9082368],[10.2891904,43.90824],[10.2892712,43.908272],[10.2895368,43.9084448],[10.2897304,43.9085472],[10.28986,43.9086464],[10.2899176,43.9087168],[10.2899968,43.9088736],[10.2900288,43.9089856],[10.2900912,43.909088],[10.2901968,43.90936],[10.2903168,43.9095392],[10.2903672,43.9096832],[10.2904256,43.9097632],[10.2904664,43.9098752],[10.290488,43.9100032],[10.2904552,43.910144],[10.2902616,43.910496],[10.2901272,43.9106496],[10.2900168,43.9107392],[10.2899064,43.910928],[10.2898016,43.9111744],[10.28978,43.9111904],[10.289748,43.9111872],[10.289868,43.9109216],[10.2900496,43.9107008],[10.2902536,43.9105088],[10.2902984,43.9104384],[10.2903744,43.9102944],[10.2904432,43.9100416],[10.2904152,43.9098272],[10.2901232,43.9092384],[10.2900752,43.9090944],[10.289904,43.9087488],[10.2897832,43.908592],[10.2897168,43.9085344],[10.2896744,43.9084448],[10.2895008,43.908368],[10.28934,43.9082656],[10.2891816,43.9082112],[10.2889728,43.9081984],[10.2888448,43.9082208],[10.288788,43.9082464],[10.2884448,43.90832],[10.2881848,43.9084288],[10.2881232,43.9084192],[10.2880672,43.9083648],[10.288072,43.9083392],[10.2881048,43.9083136],[10.288272,43.9082304],[10.2886848,43.9081408],[10.2888416,43.908128],[10.28918,43.908],[10.2892936,43.9079904],[10.2893488,43.9079616],[10.2894736,43.9079616],[10.289648,43.9080192],[10.28998,43.908208],[10.2901296,43.908272],[10.2905256,43.9083904],[10.2906504,43.908416],[10.2908848,43.9084192],[10.2911264,43.9084576],[10.2914112,43.9085888],[10.2914936,43.908608],[10.2916232,43.908608],[10.2919232,43.9085632],[10.2922472,43.908576],[10.2926056,43.908656],[10.292916,43.9087552],[10.2931496,43.9088672],[10.2933464,43.9089056],[10.2942288,43.9089536],[10.29452,43.908944],[10.29482,43.9089856],[10.2949448,43.9089824],[10.2952568,43.9088928],[10.2953392,43.9088896],[10.2954504,43.9089152],[10.2957296,43.9090816],[10.2961032,43.9094016],[10.2963848,43.9095392],[10.2966232,43.9095968],[10.2967832,43.9095776],[10.296848,43.9095488],[10.2970152,43.9095168],[10.29712,43.9095296],[10.2971536,43.9095488],[10.297232,43.9096096],[10.2973632,43.9097504],[10.2974384,43.9098784],[10.297432,43.9099872],[10.2973736,43.9101184],[10.2973536,43.910224],[10.297368,43.9102784],[10.2974784,43.9103968],[10.29766,43.910512],[10.2978784,43.9105728],[10.2981104,43.9105984],[10.2983312,43.9105984],[10.2984752,43.9106336],[10.298576,43.91072],[10.2986496,43.9108928],[10.2987816,43.9110304],[10.2989568,43.911072],[10.2990368,43.9110592],[10.2993384,43.9109536],[10.2996304,43.9108288],[10.3000584,43.910544],[10.3001784,43.9105376],[10.3002616,43.9105536],[10.3006112,43.9107104],[10.3006552,43.9107136],[10.300732,43.910688],[10.3007648,43.910672],[10.3007832,43.9106368],[10.3008344,43.910384],[10.300928,43.9101824],[10.3010168,43.91008],[10.3011568,43.9100224],[10.301412,43.9100032],[10.3021336,43.9101344],[10.3024464,43.9101472],[10.3027152,43.9101856],[10.3028768,43.9101856],[10.3029968,43.91016],[10.3030952,43.9100992],[10.303244,43.909888],[10.3032688,43.9097248],[10.3032648,43.9095072],[10.3032864,43.9094464],[10.30334,43.9094048],[10.30348,43.909344],[10.3037016,43.909328],[10.303888,43.9093728],[10.304288,43.9096832],[10.3046016,43.9098016],[10.304796,43.9098016],[10.3055664,43.9095904],[10.3057496,43.909488],[10.3061112,43.909344],[10.3063448,43.9092096],[10.3065296,43.9091712],[10.3065864,43.9091264],[10.3068352,43.909024],[10.3069432,43.9089536],[10.3070544,43.9088544],[10.307112,43.9088256],[10.3072584,43.9086848],[10.30732,43.9085472],[10.3073416,43.908432],[10.3073264,43.9083776],[10.3072344,43.9082304],[10.3070624,43.90808],[10.3066728,43.9079296],[10.3066464,43.9078752],[10.3066528,43.9078432],[10.3067352,43.9077824],[10.3070272,43.9076224],[10.3071744,43.9074976],[10.3073328,43.907264],[10.3077248,43.907008],[10.307772,43.9069568],[10.3077848,43.9068992],[10.3077568,43.9068416],[10.3075416,43.9066272],[10.307504,43.9065408],[10.3075152,43.906448],[10.3076512,43.9063008],[10.3077232,43.9062496],[10.3084088,43.9059264],[10.3088056,43.9056192],[10.309,43.905536],[10.309172,43.9055104],[10.3093416,43.9055104],[10.3096384,43.9055264],[10.3098448,43.90552],[10.3100752,43.905552],[10.3104152,43.9055584],[10.3107504,43.9055328],[10.3110664,43.905488],[10.3111632,43.90544],[10.3112088,43.9053888],[10.3112048,43.9052928],[10.3111288,43.9051296],[10.3108184,43.9046592],[10.3108016,43.9045408],[10.310812,43.9043968],[10.3107584,43.9043072],[10.3107256,43.904288],[10.3106384,43.9042784],[10.3106096,43.9042816],[10.3105552,43.90432],[10.310536,43.9043552],[10.31052,43.9044768],[10.3103648,43.9047808],[10.3103104,43.9048224],[10.3102376,43.9048512],[10.31016,43.9048512],[10.3101232,43.9048352],[10.3100256,43.904752],[10.309928,43.9046144],[10.3098584,43.90448],[10.3098272,43.9043616],[10.3096464,43.9039424],[10.309528,43.9038528],[10.3091984,43.9038208],[10.3090936,43.9037696],[10.3090184,43.9037088],[10.3088312,43.9034912],[10.3088128,43.903392]]
                    }
                }
            });
            
            // 添加轨迹线
            map.addLayer({
                id: 'track-line',
                type: 'line',
                source: 'track',
                layout: {
                    'line-join': 'round',
                    'line-cap': 'round'
                },
                paint: {
                    'line-color': '#ff4444',
                    'line-width': 4
                }
            });
            
            // 添加起点标记
            new mapboxgl.Marker({ color: 'green' })
                .setLngLat([10.3090512, 43.9034624])
                .setPopup(new mapboxgl.Popup().setHTML('<strong>起点</strong>'))
                .addTo(map);
            
            // 添加终点标记
            new mapboxgl.Marker({ color: 'red' })
                .setLngLat([10.3088128, 43.903392])
                .setPopup(new mapboxgl.Popup().setHTML('<strong>终点</strong>'))
                .addTo(map);
            
            // 自动调整视图以显示整个轨迹
            map.fitBounds([
                [10.2879864, 43.903392],
                [10.3112088, 43.9111904]
            ], { padding: 50 });
        });
    </script>
</body>
</html>