import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import { createI18n } from 'vue-i18n'

import App from './App.vue'
import { routes } from './router'
import { i18nConfig } from './i18n'

// 导入全局样式
import './assets/styles/main.css'
import 'mapbox-gl/dist/mapbox-gl.css'

// 创建应用实例
const app = createApp(App)

// 创建路由
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 创建状态管理
const pinia = createPinia()

// 创建国际化
const i18n = createI18n(i18nConfig)

// 注册插件
app.use(router)
app.use(pinia)
app.use(i18n)

// 挂载应用
app.mount('#app')

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('🚀 MapboxGL Better 应用已启动')
  console.log('📍 当前环境:', import.meta.env.MODE)
  console.log('🌐 Vue版本:', app.version)
}
