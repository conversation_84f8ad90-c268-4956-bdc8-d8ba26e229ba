import type { RouteRecordRaw } from 'vue-router'

// 懒加载组件
const MapView = () => import('@/views/MapView.vue')
const WorkoutView = () => import('@/views/WorkoutView.vue')
const DemoView = () => import('@/views/DemoView.vue')

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    redirect: '/map'
  },
  {
    path: '/map',
    name: 'Map',
    component: MapView,
    meta: {
      title: 'map.title',
      description: 'map.description'
    }
  },
  {
    path: '/workout/:workoutId?',
    name: 'Workout',
    component: WorkoutView,
    meta: {
      title: 'workout.title',
      description: 'workout.description'
    }
  },
  {
    path: '/demo',
    name: 'Demo',
    component: DemoView,
    meta: {
      title: 'demo.title',
      description: 'demo.description'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundView.vue')
  }
]
