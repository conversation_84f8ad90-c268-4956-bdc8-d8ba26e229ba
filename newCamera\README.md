# newCamera - 3D相机路径生成库

一个用于从GPS轨迹数据生成平滑3D相机路径的JavaScript库。

## 功能特性

- 🎯 **GPX文件解析** - 支持标准GPX格式的GPS轨迹文件
- 🎬 **平滑路径生成** - 使用Catmull-Rom样条插值生成平滑的相机路径
- 🎮 **动画控制** - 完整的播放、暂停、跳转控制功能
- 📐 **3D数学库** - 内置3D向量运算和坐标转换
- 🗺️ **地理坐标支持** - 经纬度与笛卡尔坐标系转换
- ⚡ **高性能** - 优化的算法确保流畅的动画效果

## 快速开始

### 基本使用

```javascript
import { CameraAnimationController, GPXParser } from './newCamera/index.js';

// 创建动画控制器
const controller = new CameraAnimationController({
  duration: 30000, // 30秒动画
  onUpdate: (state) => {
    // 更新相机位置
    console.log('当前进度:', state.progress);
    console.log('观察点:', state.lookAt);
  },
  onComplete: () => {
    console.log('动画完成');
  }
});

// 从GPX文件初始化
controller.initializeFromGPXURL('./workout_track.gpx')
  .then(() => {
    console.log('初始化成功');
    controller.play(); // 开始播放动画
  })
  .catch(error => {
    console.error('初始化失败:', error);
  });
```

### 手动解析GPX

```javascript
import { GPXParser, CameraPath } from './newCamera/index.js';

// 解析GPX文件
const trackPoints = await GPXParser.loadGPXFromURL('./workout_track.gpx');

// 验证数据
const validation = GPXParser.validateTrackPoints(trackPoints);
if (!validation.isValid) {
  console.error('数据验证失败:', validation.errors);
  return;
}

// 创建相机路径
const cameraPath = new CameraPath(trackPoints);

// 获取指定时间的观察点
const lookAtPoint = cameraPath.getLookAtPoint(0.5); // 50%进度处的点
```

### 与Mapbox GL JS集成

```javascript
import { CameraAnimationController } from './newCamera/index.js';

// 创建Mapbox地图
const map = new mapboxgl.Map({
  container: 'map',
  style: 'mapbox://styles/mapbox/satellite-v9',
  center: [0, 0],
  zoom: 10
});

// 创建相机控制器
const controller = new CameraAnimationController({
  duration: 40000,
  onUpdate: (state) => {
    // 更新Mapbox相机
    map.easeTo({
      center: [state.lookAt.lon, state.lookAt.lat],
      zoom: 15,
      bearing: 0,
      pitch: 60,
      duration: 100
    });
  }
});

// 初始化并播放
await controller.initializeFromGPXURL('./workout_track.gpx');
controller.play();
```

## API文档

### CameraAnimationController

主要的动画控制类。

#### 构造函数

```javascript
new CameraAnimationController(options)
```

**参数:**
- `options.duration` - 动画持续时间（毫秒），默认40000
- `options.onUpdate` - 更新回调函数
- `options.onComplete` - 完成回调函数

#### 方法

- `initializeFromGPXURL(url, options)` - 从GPX URL初始化
- `initializeFromTrackPoints(points, options)` - 从轨迹点数组初始化
- `play()` - 开始播放
- `pause()` - 暂停播放
- `stop()` - 停止并重置
- `seekTo(time)` - 跳转到指定时间
- `seekToProgress(progress)` - 跳转到指定进度(0-1)
- `getState()` - 获取当前状态

### GPXParser

GPX文件解析工具。

#### 静态方法

- `parseGPX(gpxContent)` - 解析GPX字符串
- `loadGPXFromURL(url)` - 从URL加载GPX文件
- `parseGPXFile(file)` - 解析文件对象
- `validateTrackPoints(points)` - 验证轨迹点数据

### CameraPath

相机路径生成器。

#### 构造函数

```javascript
new CameraPath(points)
```

#### 方法

- `getLookAtPoint(t)` - 获取观察点(t: 0-1)
- `getNarrowLookAtPoint(t)` - 获取窄观察点(t: 0-1)
- `getCameraPosition(ref, distance, azimuth, elevation)` - 计算相机位置

## 配置选项

### 相机路径配置

```javascript
const options = {
  duration: 30000,                    // 动画时长
  numberOfLookAtKeys: 16,             // 观察点数量
  numberOfNarrowLookAtKeys: 64,       // 窄观察点数量
  numberOfCameraKeys: 12              // 相机关键帧数量
};
```

## 数据格式

### 轨迹点格式

```javascript
{
  latitude: 43.9034624,    // 纬度
  longitude: 10.3090512,   // 经度
  altitude: 181.4,         // 高度（米）
  timestamp: 1651507906000 // 时间戳
}
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

需要支持ES6模块和现代JavaScript特性。

## 许可证

MIT License
