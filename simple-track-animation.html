<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单轨迹动画 - 相机旋转</title>
    
    <!-- MapboxGL CSS -->
    <link href="https://api.mapbox.com/mapbox-gl-js/v3.14.0/mapbox-gl.css" rel="stylesheet">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }

        #map {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
        }

        #controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 100;
        }

        #controls button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-weight: bold;
        }

        #controls button:hover {
            background: #45a049;
        }
    </style>
</head>

<body>
    <div id="controls">
        <button id="toggleAnimation">开始相机旋转</button>
        <button id="showTrack">显示轨迹</button>
    </div>

    <div id='map'></div>

    <!-- MapboxGL JS -->
    <script src="https://api.mapbox.com/mapbox-gl-js/v3.14.0/mapbox-gl.js"></script>
    
    <!-- 配置文件 -->
    <script src="config.js"></script>
    
    <script>
        // 使用配置文件中的 Access Token
        mapboxgl.accessToken = CONFIG.MAPBOX_ACCESS_TOKEN;

        // 全局变量
        let map;
        let trackData = [];
        let isAnimating = false;
        let animationId = null;
        let currentPointIndex = 0;

        // 初始化地图
        function initMap() {
            map = new mapboxgl.Map({
                container: 'map',
                 style: 'mapbox://styles/mapbox/standard-satellite'
                center: [10.3090512, 43.9034624], // GPX轨迹的起始点
                zoom: 15,
                pitch: 70,
                bearing: 0,
                antialias: true
            });

            // 地图加载完成后的处理
            map.on('style.load', () => {
                setupTerrain();
                loadTrackData();
            });
        }

        // 设置3D地形
        function setupTerrain() {
            // 添加DEM数据源
            map.addSource('mapbox-dem', {
                'type': 'raster-dem',
                'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
                'tileSize': 512,
                'maxzoom': 14
            });

            // 设置地形
            map.setTerrain({
                'source': 'mapbox-dem',
                'exaggeration': 2.0
            });

            // 添加天空层
            map.addLayer({
                'id': 'sky',
                'type': 'sky',
                'paint': {
                    'sky-type': 'atmosphere',
                    'sky-atmosphere-sun': [0.0, 90.0],
                    'sky-atmosphere-sun-intensity': 15
                }
            });

            console.log('3D地形已设置');
        }

        // 加载GPX轨迹数据
        async function loadTrackData() {
            try {
                const response = await fetch('workout_track.gpx');
                const gpxText = await response.text();
                
                // 解析GPX数据
                const parser = new DOMParser();
                const gpxDoc = parser.parseFromString(gpxText, 'text/xml');
                const trackPoints = gpxDoc.querySelectorAll('trkpt');
                
                trackData = Array.from(trackPoints).map((point, index) => {
                    const lat = parseFloat(point.getAttribute('lat'));
                    const lon = parseFloat(point.getAttribute('lon'));
                    const ele = parseFloat(point.querySelector('ele')?.textContent || '0');
                    
                    return {
                        coordinates: [lon, lat],
                        elevation: ele,
                        index: index
                    };
                });

                console.log(`已加载 ${trackData.length} 个轨迹点`);
                
                // 设置地图视图到轨迹范围
                fitMapToTrack();
                
            } catch (error) {
                console.error('加载GPX数据失败:', error);
                alert('无法加载GPX文件，请确保文件存在');
            }
        }

        // 创建轨迹线
        function createTrackLine() {
            if (trackData.length === 0) return;

            // 完整轨迹线
            map.addSource('track-line', {
                'type': 'geojson',
                'data': {
                    'type': 'Feature',
                    'properties': {},
                    'geometry': {
                        'type': 'LineString',
                        'coordinates': trackData.map(point => point.coordinates)
                    }
                }
            });

            map.addLayer({
                'id': 'track-line-layer',
                'type': 'line',
                'source': 'track-line',
                'layout': {
                    'line-join': 'round',
                    'line-cap': 'round'
                },
                'paint': {
                    'line-color': '#FF5722',
                    'line-width': 4,
                    'line-opacity': 0.8
                }
            });

            // 起点标记
            map.addSource('start-point', {
                'type': 'geojson',
                'data': {
                    'type': 'Feature',
                    'properties': {},
                    'geometry': {
                        'type': 'Point',
                        'coordinates': trackData[0].coordinates
                    }
                }
            });

            map.addLayer({
                'id': 'start-point-layer',
                'type': 'circle',
                'source': 'start-point',
                'paint': {
                    'circle-radius': 8,
                    'circle-color': '#4CAF50',
                    'circle-stroke-color': '#FFFFFF',
                    'circle-stroke-width': 2
                }
            });

            // 终点标记
            map.addSource('end-point', {
                'type': 'geojson',
                'data': {
                    'type': 'Feature',
                    'properties': {},
                    'geometry': {
                        'type': 'Point',
                        'coordinates': trackData[trackData.length - 1].coordinates
                    }
                }
            });

            map.addLayer({
                'id': 'end-point-layer',
                'type': 'circle',
                'source': 'end-point',
                'paint': {
                    'circle-radius': 8,
                    'circle-color': '#F44336',
                    'circle-stroke-color': '#FFFFFF',
                    'circle-stroke-width': 2
                }
            });

            console.log('轨迹线已创建');
        }

        // 设置地图视图到轨迹范围
        function fitMapToTrack() {
            if (trackData.length === 0) return;

            const coordinates = trackData.map(point => point.coordinates);
            const bounds = coordinates.reduce((bounds, coord) => {
                return bounds.extend(coord);
            }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]));

            map.fitBounds(bounds, {
                padding: 100,
                pitch: 70,
                bearing: 0
            });
        }

        // 自动旋转动画 - 参考terrain-demo.html
        function startAnimation() {
            isAnimating = true;
            document.getElementById('toggleAnimation').textContent = '停止相机旋转';
            
            function rotate() {
                if (!isAnimating) return;
                const bearing = map.getBearing();
                map.setBearing(bearing + 0.3); // 稍微快一点的旋转速度
                animationId = requestAnimationFrame(rotate);
            }
            
            animationId = requestAnimationFrame(rotate);
        }

        // 停止动画
        function stopAnimation() {
            isAnimating = false;
            document.getElementById('toggleAnimation').textContent = '开始相机旋转';
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
        }

        // 切换动画状态
        function toggleAnimation() {
            if (isAnimating) {
                stopAnimation();
            } else {
                startAnimation();
            }
        }

        // 显示/隐藏轨迹
        function toggleTrack() {
            const button = document.getElementById('showTrack');
            
            if (map.getLayer('track-line-layer')) {
                // 隐藏轨迹
                map.removeLayer('track-line-layer');
                map.removeLayer('start-point-layer');
                map.removeLayer('end-point-layer');
                map.removeSource('track-line');
                map.removeSource('start-point');
                map.removeSource('end-point');
                button.textContent = '显示轨迹';
            } else {
                // 显示轨迹
                createTrackLine();
                button.textContent = '隐藏轨迹';
            }
        }

        // 事件监听器
        function setupEventListeners() {
            document.getElementById('toggleAnimation').addEventListener('click', toggleAnimation);
            document.getElementById('showTrack').addEventListener('click', toggleTrack);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initMap();
            setupEventListeners();
        });
    </script>
</body>

</html>
