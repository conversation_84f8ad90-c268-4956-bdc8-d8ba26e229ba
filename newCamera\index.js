/**
 * newCamera - 3D相机路径生成库
 * 主入口文件
 */

// 导入所有核心类和函数
import { Vec3, radians, degrees } from './Vec3.js';
import { CatmullRomSpline, CatmullRomSegment } from './CatmullRomSpline.js';
import { Track, InterpolatedChain } from './Track.js';
import { CameraPath, TRACK_PROGRESS_FACTOR } from './CameraPath.js';
import { GPXParser } from './GPXParser.js';

// 导出所有核心类和函数
export { Vec3, radians, degrees } from './Vec3.js';
export { CatmullRomSpline, CatmullRomSegment } from './CatmullRomSpline.js';
export { Track, InterpolatedChain } from './Track.js';
export { CameraPath, TRACK_PROGRESS_FACTOR } from './CameraPath.js';
export { GPXParser } from './GPXParser.js';

/**
 * 相机动画控制器
 * 提供完整的相机动画控制功能
 */
export class CameraAnimationController {
  constructor(options = {}) {
    this.isPlaying = false;
    this.currentTime = 0;
    this.duration = options.duration || 40000; // 默认40秒
    this.onUpdate = options.onUpdate || (() => {});
    this.onComplete = options.onComplete || (() => {});
    
    this.cameraPath = null;
    this.animationId = null;
    this.startTime = null;
  }

  /**
   * 从GPX数据初始化相机路径
   * @param {Array} trackPoints - 轨迹点数组
   * @param {Object} options - 配置选项
   */
  initializeFromTrackPoints(trackPoints, options = {}) {
    this.cameraPath = new CameraPath(trackPoints);
    
    // 应用配置选项
    if (options.duration) this.duration = options.duration;
    if (options.numberOfLookAtKeys) {
      this.cameraPath.numberOfLookAtKeys = options.numberOfLookAtKeys;
    }
    if (options.numberOfNarrowLookAtKeys) {
      this.cameraPath.numberOfNarrowLookAtKeys = options.numberOfNarrowLookAtKeys;
    }
    
    // 重新初始化路径
    if (trackPoints.length > 0) {
      this.cameraPath.initializeFromPoints(trackPoints);
    }
  }

  /**
   * 从GPX文件URL初始化
   * @param {string} gpxUrl - GPX文件URL
   * @param {Object} options - 配置选项
   * @returns {Promise} 初始化Promise
   */
  async initializeFromGPXURL(gpxUrl, options = {}) {
    try {
      const trackPoints = await GPXParser.loadGPXFromURL(gpxUrl);
      const validation = GPXParser.validateTrackPoints(trackPoints);
      
      if (!validation.isValid) {
        throw new Error('GPX数据验证失败: ' + validation.errors.join(', '));
      }
      
      if (validation.warnings.length > 0) {
        console.warn('GPX数据警告:', validation.warnings);
      }
      
      this.initializeFromTrackPoints(trackPoints, options);
      return { success: true, pointCount: trackPoints.length };
    } catch (error) {
      console.error('从GPX URL初始化失败:', error);
      throw error;
    }
  }

  /**
   * 开始动画
   */
  play() {
    if (this.isPlaying || !this.cameraPath) return;
    
    this.isPlaying = true;
    this.startTime = performance.now() - this.currentTime;
    this.animate();
  }

  /**
   * 暂停动画
   */
  pause() {
    this.isPlaying = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * 停止动画并重置
   */
  stop() {
    this.pause();
    this.currentTime = 0;
  }

  /**
   * 跳转到指定时间
   * @param {number} time - 时间（毫秒）
   */
  seekTo(time) {
    this.currentTime = Math.max(0, Math.min(time, this.duration));
    if (this.isPlaying) {
      this.startTime = performance.now() - this.currentTime;
    }
    this.updateCamera();
  }

  /**
   * 跳转到指定进度
   * @param {number} progress - 进度 (0-1)
   */
  seekToProgress(progress) {
    const time = progress * this.duration;
    this.seekTo(time);
  }

  /**
   * 动画循环
   */
  animate() {
    if (!this.isPlaying) return;

    const now = performance.now();
    this.currentTime = now - this.startTime;

    if (this.currentTime >= this.duration) {
      this.currentTime = this.duration;
      this.isPlaying = false;
      this.onComplete();
    }

    this.updateCamera();

    if (this.isPlaying) {
      this.animationId = requestAnimationFrame(() => this.animate());
    }
  }

  /**
   * 更新相机状态
   */
  updateCamera() {
    if (!this.cameraPath) return;

    const progress = this.currentTime / this.duration;
    const easedProgress = this.cameraPath.cameraEasing(progress);
    
    // 获取观察点
    const lookAtPoint = this.cameraPath.getLookAtPoint(easedProgress);
    const narrowLookAtPoint = this.cameraPath.getNarrowLookAtPoint(easedProgress);
    
    if (lookAtPoint && narrowLookAtPoint) {
      // 转换为地理坐标
      const lookAtGeo = this.cameraPath.getLatLonAltFromPseudoCartesianCoordinates(lookAtPoint);
      const narrowLookAtGeo = this.cameraPath.getLatLonAltFromPseudoCartesianCoordinates(narrowLookAtPoint);

      // 调用更新回调
      this.onUpdate({
        progress: progress,
        easedProgress: easedProgress,
        currentTime: this.currentTime,
        lookAt: lookAtGeo,
        narrowLookAt: narrowLookAtGeo,
        bounds: this.cameraPath.bounds
      });
    }
  }

  /**
   * 获取当前状态
   * @returns {Object} 当前状态信息
   */
  getState() {
    return {
      isPlaying: this.isPlaying,
      currentTime: this.currentTime,
      duration: this.duration,
      progress: this.currentTime / this.duration,
      hasPath: !!this.cameraPath
    };
  }
}
