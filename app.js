// MapboxGL Demo 应用
class MapboxDemo {
    constructor() {
        // 使用配置文件中的 Access Token
        mapboxgl.accessToken = CONFIG.MAPBOX_ACCESS_TOKEN;

        this.rotationAnimationId = null;
        this.isRotating = false;
        this.is3DEnabled = false;

        this.initMap();
        this.setupEventListeners();
    }

    initMap() {
        // 使用配置文件初始化地图
        this.map = new mapboxgl.Map({
            container: 'map',
            style: CONFIG.DEFAULT_MAP_CONFIG.style,
            center: CONFIG.DEFAULT_MAP_CONFIG.center,
            zoom: CONFIG.DEFAULT_MAP_CONFIG.zoom,
            projection: CONFIG.DEFAULT_MAP_CONFIG.projection
        });

        // 添加控件
        this.addControls();
        
        // 地图加载完成后的事件
        this.map.on('load', () => {
            console.log('地图加载完成');
            this.updateCoordinateInfo();
        });

        // 鼠标移动事件 - 更新坐标信息
        this.map.on('mousemove', (e) => {
            this.updateCoordinateDisplay(e.lngLat);
        });

        // 地图移动事件 - 更新缩放级别
        this.map.on('move', () => {
            this.updateCoordinateInfo();
        });

        // 样式加载完成事件
        this.map.on('style.load', () => {
            // 添加雾效果
            this.map.setFog({});
        });
    }

    addControls() {
        // 添加全屏控件
        this.map.addControl(new mapboxgl.FullscreenControl(), 'top-right');

        // 添加定位控件
        this.map.addControl(new mapboxgl.GeolocateControl({
            positionOptions: {
                enableHighAccuracy: true
            },
            trackUserLocation: true,
            showUserHeading: true
        }), 'top-right');

        // 添加比例尺控件
        this.map.addControl(new mapboxgl.ScaleControl({
            maxWidth: 200,
            unit: 'metric'
        }), 'bottom-left');

        // 添加导航控件
        this.map.addControl(new mapboxgl.NavigationControl(), 'top-right');

        // 添加地理搜索控件（如果可用）
        if (typeof MapboxGeocoder !== 'undefined') {
            this.map.addControl(new MapboxGeocoder({
                accessToken: mapboxgl.accessToken,
                mapboxgl: mapboxgl,
                placeholder: '搜索地点...'
            }), 'top-left');
        }
    }

    setupEventListeners() {
        // 样式选择器
        document.getElementById('styleSelect').addEventListener('change', (e) => {
            this.changeMapStyle(e.target.value);
        });

        // 位置选择器
        document.getElementById('locationSelect').addEventListener('change', (e) => {
            const locationKey = e.target.value;
            if (locationKey && CONFIG.PRESET_LOCATIONS[locationKey]) {
                const location = CONFIG.PRESET_LOCATIONS[locationKey];
                this.flyToLocation(location.coordinates, location.name, location.zoom);
            }
        });

        // 自定义瓦片源选择器
        document.getElementById('customTileSelect').addEventListener('change', (e) => {
            const tileSourceKey = e.target.value;
            if (tileSourceKey && CONFIG.CUSTOM_TILE_SOURCES[tileSourceKey]) {
                this.addCustomTileSource(tileSourceKey);
            }
        });

        // 切换旋转
        document.getElementById('toggleRotation').addEventListener('click', () => {
            this.toggleRotation();
        });

        // 切换3D地形
        document.getElementById('toggle3D').addEventListener('click', () => {
            this.toggle3DTerrain();
        });

        // 重置视图
        document.getElementById('resetView').addEventListener('click', () => {
            this.resetView();
        });

        // 地形夸张系数滑块
        document.getElementById('exaggerationSlider').addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            document.getElementById('exaggerationValue').textContent = value;
            this.updateTerrainExaggeration(value);
        });

        // 视角倾斜滑块
        document.getElementById('pitchSlider').addEventListener('input', (e) => {
            const value = parseInt(e.target.value);
            document.getElementById('pitchValue').textContent = value;
            this.updateMapPitch(value);
        });
    }

    changeMapStyle(styleId) {
        const styleUrl = `mapbox://styles/mapbox/${styleId}`;
        this.map.setStyle(styleUrl);
        console.log(`切换地图样式: ${styleId}`);
    }

    flyToLocation(coordinates, name, zoom = 12) {
        this.map.flyTo({
            center: coordinates,
            zoom: zoom,
            speed: CONFIG.ANIMATION_CONFIG.flySpeed,
            curve: 1.42,
            pitch: CONFIG.ANIMATION_CONFIG.flyPitch,
            bearing: 0
        });
        console.log(`飞行到: ${name}`);

        // 重置位置选择器
        document.getElementById('locationSelect').value = '';
    }

    toggleRotation() {
        if (this.isRotating) {
            this.stopRotation();
        } else {
            this.startRotation();
        }
    }

    startRotation() {
        this.isRotating = true;
        document.getElementById('toggleRotation').textContent = '停止旋转';
        
        const rotateCamera = (timestamp) => {
            if (!this.isRotating) return;
            
            // 获取当前方位角并增加
            const bearing = this.map.getBearing();
            this.map.rotateTo(bearing + 0.5, { duration: 0 });
            
            this.rotationAnimationId = requestAnimationFrame(rotateCamera);
        };
        
        this.rotationAnimationId = requestAnimationFrame(rotateCamera);
        console.log('开始相机旋转');
    }

    stopRotation() {
        this.isRotating = false;
        if (this.rotationAnimationId) {
            cancelAnimationFrame(this.rotationAnimationId);
            this.rotationAnimationId = null;
        }
        document.getElementById('toggleRotation').textContent = '开始旋转';
        console.log('停止相机旋转');
    }

    toggle3DTerrain() {
        if (this.is3DEnabled) {
            this.disable3DTerrain();
        } else {
            this.enable3DTerrain();
        }
    }

    enable3DTerrain() {
        try {
            // 添加DEM数据源（如果不存在）
            if (!this.map.getSource('mapbox-dem')) {
                this.map.addSource('mapbox-dem', {
                    'type': 'raster-dem',
                    'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
                    'tileSize': 512,
                    'maxzoom': 14
                });
                console.log('已添加 DEM 数据源');
            }

            // 设置地形，使用配置文件中的夸张系数
            this.map.setTerrain({
                'source': 'mapbox-dem',
                'exaggeration': CONFIG.ANIMATION_CONFIG.terrainExaggeration
            });

            // 添加天空层以增强3D效果
            if (!this.map.getLayer('sky')) {
                this.map.addLayer({
                    'id': 'sky',
                    'type': 'sky',
                    'paint': {
                        'sky-type': 'atmosphere',
                        'sky-atmosphere-sun': [0.0, 0.0],
                        'sky-atmosphere-sun-intensity': 15
                    }
                });
            }

            this.is3DEnabled = true;
            document.getElementById('toggle3D').textContent = '关闭3D地形';

            // 显示地形控制面板
            document.getElementById('terrainControls').style.display = 'block';

            console.log('启用3D地形，夸张系数:', CONFIG.ANIMATION_CONFIG.terrainExaggeration);

        } catch (error) {
            console.error('启用3D地形失败:', error);
            alert('启用3D地形失败: ' + error.message);
        }
    }

    disable3DTerrain() {
        try {
            // 移除地形
            this.map.setTerrain(null);

            // 移除天空层
            if (this.map.getLayer('sky')) {
                this.map.removeLayer('sky');
            }

            this.is3DEnabled = false;
            document.getElementById('toggle3D').textContent = '开启3D地形';

            // 隐藏地形控制面板
            document.getElementById('terrainControls').style.display = 'none';

            console.log('禁用3D地形');

        } catch (error) {
            console.error('禁用3D地形失败:', error);
        }
    }

    resetView() {
        this.stopRotation();
        this.map.flyTo({
            center: CONFIG.DEFAULT_MAP_CONFIG.center,
            zoom: CONFIG.DEFAULT_MAP_CONFIG.zoom,
            pitch: 0,
            bearing: 0,
            speed: 1.2
        });
        console.log('重置视图');
    }

    addCustomTileSource(sourceKey) {
        const sourceConfig = CONFIG.CUSTOM_TILE_SOURCES[sourceKey];
        if (!sourceConfig) {
            console.error(`未找到瓦片源配置: ${sourceKey}`);
            return;
        }

        // 移除现有的自定义图层
        this.removeCustomTileLayers();

        // 添加新的瓦片源
        const sourceId = `custom-tiles-${sourceKey}`;
        const layerId = `custom-layer-${sourceKey}`;

        try {
            // 添加数据源
            this.map.addSource(sourceId, {
                type: sourceConfig.type,
                tiles: sourceConfig.tiles,
                tileSize: sourceConfig.tileSize || 256,
                maxzoom: sourceConfig.maxzoom || 18,
                attribution: sourceConfig.attribution || ''
            });

            // 添加图层
            this.map.addLayer({
                id: layerId,
                type: 'raster',
                source: sourceId,
                paint: {
                    'raster-opacity': 0.8
                }
            });

            console.log(`已添加自定义瓦片源: ${sourceConfig.name}`);

            // 重置选择器
            document.getElementById('customTileSelect').value = '';

        } catch (error) {
            console.error('添加自定义瓦片源失败:', error);
            alert(`添加瓦片源失败: ${error.message}`);
        }
    }

    removeCustomTileLayers() {
        // 移除所有自定义瓦片图层和数据源
        const layers = this.map.getStyle().layers;
        const sources = this.map.getStyle().sources;

        // 移除自定义图层
        layers.forEach(layer => {
            if (layer.id.startsWith('custom-layer-')) {
                try {
                    this.map.removeLayer(layer.id);
                } catch (e) {
                    console.warn(`移除图层失败: ${layer.id}`, e);
                }
            }
        });

        // 移除自定义数据源
        Object.keys(sources).forEach(sourceId => {
            if (sourceId.startsWith('custom-tiles-')) {
                try {
                    this.map.removeSource(sourceId);
                } catch (e) {
                    console.warn(`移除数据源失败: ${sourceId}`, e);
                }
            }
        });
    }

    updateCoordinateDisplay(lngLat) {
        document.getElementById('longitude').textContent = lngLat.lng.toFixed(6);
        document.getElementById('latitude').textContent = lngLat.lat.toFixed(6);
    }

    updateCoordinateInfo() {
        const center = this.map.getCenter();
        const zoom = this.map.getZoom();
        
        document.getElementById('longitude').textContent = center.lng.toFixed(6);
        document.getElementById('latitude').textContent = center.lat.toFixed(6);
        document.getElementById('zoom').textContent = zoom.toFixed(2);
    }

    updateTerrainExaggeration(exaggeration) {
        if (this.is3DEnabled && this.map.getSource('mapbox-dem')) {
            try {
                this.map.setTerrain({
                    'source': 'mapbox-dem',
                    'exaggeration': exaggeration
                });
                console.log('更新地形夸张系数:', exaggeration);
            } catch (error) {
                console.error('更新地形夸张系数失败:', error);
            }
        }
    }

    updateMapPitch(pitch) {
        try {
            this.map.setPitch(pitch);
            console.log('更新视角倾斜:', pitch);
        } catch (error) {
            console.error('更新视角倾斜失败:', error);
        }
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    try {
        const demo = new MapboxDemo();
        console.log('MapboxGL Demo 初始化成功');
    } catch (error) {
        console.error('MapboxGL Demo 初始化失败:', error);
        alert('地图初始化失败，请检查网络连接和 Access Token 设置');
    }
});
