/**
 * GPX文件解析器
 * 解析GPX格式的GPS轨迹文件
 */
export class GPXParser {
  /**
   * 解析GPX文件内容
   * @param {string} gpxContent - GPX文件内容
   * @returns {Array} 轨迹点数组
   */
  static parseGPX(gpxContent) {
    try {
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(gpxContent, 'text/xml');
      
      // 检查解析错误
      const parseError = xmlDoc.querySelector('parsererror');
      if (parseError) {
        throw new Error('GPX解析错误: ' + parseError.textContent);
      }

      const trackPoints = xmlDoc.querySelectorAll('trkpt');
      const points = [];

      trackPoints.forEach((trkpt, index) => {
        const lat = parseFloat(trkpt.getAttribute('lat'));
        const lon = parseFloat(trkpt.getAttribute('lon'));
        
        // 获取高度信息
        const eleElement = trkpt.querySelector('ele');
        const alt = eleElement ? parseFloat(eleElement.textContent) : 0;
        
        // 获取时间信息
        const timeElement = trkpt.querySelector('time');
        let timestamp = index; // 默认使用索引作为时间戳
        
        if (timeElement) {
          const timeStr = timeElement.textContent;
          const date = new Date(timeStr);
          if (!isNaN(date.getTime())) {
            timestamp = date.getTime();
          }
        }

        // 验证坐标有效性
        if (!isNaN(lat) && !isNaN(lon) && 
            lat >= -90 && lat <= 90 && 
            lon >= -180 && lon <= 180) {
          points.push({
            latitude: lat,
            longitude: lon,
            altitude: alt,
            timestamp: timestamp
          });
        }
      });

      return points;
    } catch (error) {
      console.error('GPX解析失败:', error);
      throw error;
    }
  }

  /**
   * 从URL加载并解析GPX文件
   * @param {string} url - GPX文件URL
   * @returns {Promise<Array>} 轨迹点数组的Promise
   */
  static async loadGPXFromURL(url) {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`);
      }
      
      const gpxContent = await response.text();
      return this.parseGPX(gpxContent);
    } catch (error) {
      console.error('加载GPX文件失败:', error);
      throw error;
    }
  }

  /**
   * 从文件输入元素解析GPX文件
   * @param {File} file - 文件对象
   * @returns {Promise<Array>} 轨迹点数组的Promise
   */
  static async parseGPXFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        try {
          const gpxContent = event.target.result;
          const points = this.parseGPX(gpxContent);
          resolve(points);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      
      reader.readAsText(file);
    });
  }

  /**
   * 验证轨迹点数据
   * @param {Array} points - 轨迹点数组
   * @returns {Object} 验证结果
   */
  static validateTrackPoints(points) {
    const result = {
      isValid: true,
      errors: [],
      warnings: [],
      pointCount: points.length
    };

    if (points.length === 0) {
      result.isValid = false;
      result.errors.push('没有找到有效的轨迹点');
      return result;
    }

    if (points.length < 4) {
      result.warnings.push('轨迹点数量较少，可能影响曲线平滑度');
    }

    // 检查坐标范围
    points.forEach((point, index) => {
      if (point.latitude < -90 || point.latitude > 90) {
        result.errors.push(`点${index + 1}: 纬度超出范围 (${point.latitude})`);
        result.isValid = false;
      }
      
      if (point.longitude < -180 || point.longitude > 180) {
        result.errors.push(`点${index + 1}: 经度超出范围 (${point.longitude})`);
        result.isValid = false;
      }
    });

    return result;
  }
}
