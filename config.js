// MapboxGL 配置文件
const CONFIG = {
    // Mapbox Access Token - 请替换为您自己的 token
    // 获取方式：https://account.mapbox.com/access-tokens/
    MAPBOX_ACCESS_TOKEN: 'pk.eyJ1IjoiYXNkaWdpdGFsIiwiYSI6ImNrcTloMGg5ejAwbGMyb3B0dDRncHBlYTIifQ.XxD8GnrgEGrzkc7TiRUE-A',
    
    // 默认地图配置
    DEFAULT_MAP_CONFIG: {
        style: 'mapbox://styles/mapbox/streets-v11',
        center: [113.2644, 23.1291], // 北京坐标
        zoom: 10,
        projection: 'mercator'
    },
    
    // 预设位置
    PRESET_LOCATIONS: {
        beijing: {
            name: '北京',
            coordinates: [116.4074, 39.9042],
            zoom: 12
        },
        shanghai: {
            name: '上海',
            coordinates: [121.4737, 31.2304],
            zoom: 12
        },
        guangzhou: {
            name: '广州',
            coordinates: [113.2644, 23.1291],
            zoom: 12
        },
        shenzhen: {
            name: '深圳',
            coordinates: [114.0579, 22.5431],
            zoom: 12
        }
    },
    
    // 地图样式选项
    MAP_STYLES: {
        'streets-v11': 'Streets（街道）',
        'dark-v10': 'Dark（深色）',
        'satellite-streets-v11': 'Satellite（卫星）',
        'outdoors-v11': 'Outdoors（户外）',
        'light-v10': 'Light（浅色）'
    },

    // 自定义瓦片源配置
    CUSTOM_TILE_SOURCES: {
        // Mapbox v4 卫星瓦片
        'mapbox-satellite-v4': {
            name: 'Mapbox 卫星 (v4 API)',
            type: 'raster',
            tiles: [
                'https://api.mapbox.com/v4/mapbox.satellite/{z}/{x}/{y}.webp?sku=101RJyZcexzmg&access_token=' + 'pk.eyJ1IjoiYXNkaWdpdGFsIiwiYSI6ImNrcTloMGg5ejAwbGMyb3B0dDRncHBlYTIifQ.XxD8GnrgEGrzkc7TiRUE-A'
            ],
            tileSize: 256,
            maxzoom: 18
        },
        // OpenStreetMap 瓦片
        'osm': {
            name: 'OpenStreetMap',
            type: 'raster',
            tiles: [
                'https://tile.openstreetmap.org/{z}/{x}/{y}.png'
            ],
            tileSize: 256,
            maxzoom: 19,
            attribution: '© OpenStreetMap contributors'
        },
        // 高德地图瓦片（示例）
        'amap-satellite': {
            name: '高德卫星图',
            type: 'raster',
            tiles: [
                'https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}'
            ],
            tileSize: 256,
            maxzoom: 18
        }
    },
    
    // 动画配置
    ANIMATION_CONFIG: {
        rotationSpeed: 0.5, // 旋转速度（度/帧）
        flySpeed: 0.8,      // 飞行速度
        flyPitch: 45,       // 飞行时的倾斜角度
        terrainExaggeration: 1.5 // 地形夸张系数
    },
    
    // 控件配置
    CONTROLS_CONFIG: {
        showFullscreen: true,
        showGeolocate: true,
        showScale: true,
        showNavigation: true,
        showGeocoder: true,
        scaleUnit: 'metric', // 'metric' 或 'imperial'
        scaleMaxWidth: 200
    }
};

// 导出配置（如果在模块环境中使用）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
