# 工具函数库深度解析

## 概述

Suunto 地图应用的工具函数库（helpers）是支撑整个应用的基础设施，提供了数学计算、状态管理、照片处理等核心功能。这些工具函数虽然代码量不大，但设计精巧，体现了现代 React 应用开发中的最佳实践。

## 整体架构图

```mermaid
graph TB
    subgraph "数学工具"
        A[math.ts<br/>数值映射和缩放]
    end
    
    subgraph "React Hooks"
        B[useLoading.tsx<br/>加载状态管理]
        C[useToggle.tsx<br/>切换状态管理]
        D[usePrevious.tsx<br/>前值记录]
    end
    
    subgraph "媒体处理"
        E[photos.ts<br/>照片尺寸处理]
    end
    
    subgraph "基础工具"
        F[noOp.tsx<br/>空操作函数]
    end
    
    subgraph "应用集成"
        G[WorkoutMap.ts<br/>地图计算]
        H[usePosition.tsx<br/>动画控制]
        I[Summary组件<br/>数据验证]
        J[Developer.tsx<br/>开发工具]
    end
    
    A --> G
    B --> H
    C --> H
    D --> I
    E --> I
    F --> H
    F --> J
```

---

## 1. math.ts - 数学工具函数

### 核心函数：scale

```typescript
export const scale = (
  input: number,
  inputRange: [number, number],
  targetRange: [number, number],
): number => {
  const [targetMin, targetMax] = targetRange;
  const [inputMin, inputMax] = inputRange;
  const percent = (input - inputMin) / (inputMax - inputMin);
  return percent * (targetMax - targetMin) + targetMin;
};
```

**函数特点：**
- **线性映射**：将输入范围的值映射到目标范围
- **类型安全**：完整的 TypeScript 类型定义
- **数学精确**：基于线性插值的精确计算

### 实际应用场景

#### 1. 地图相机计算
```typescript
// WorkoutMap.ts 中的应用
let centeringBehaviour = scale(
  canvas.width / canvas.height,  // 输入：画布宽高比
  [1.25, 0.5],                  // 输入范围
  [cameraPath.lookAtPointCenteringBehaviour.getValue(cameraProgress), 1], // 目标范围
);
```

#### 2. 数据可视化
```typescript
// 将心率值映射到颜色强度
const heartRate = workout.getHeartrate()?.points[pointIndex]?.value || 0;
const colorIntensity = scale(heartRate, [60, 200], [0, 1]);
```

#### 3. 进度条映射
```typescript
// 将进度 (0-1) 映射到轨迹点索引
const pointIndex = Math.floor(scale(
  progress,
  [0, 1],
  [0, workout.getLocation()?.locationPoints?.length - 1]
));
```

**应用价值：**
- **数据归一化**：统一不同数据范围到标准区间
- **视觉映射**：数值到颜色、大小、位置的转换
- **动画插值**：平滑的动画过渡计算

---

## 2. useLoading.tsx - 加载状态管理

### Hook 设计

```typescript
export const useLoading = (initialState = false): [boolean, () => () => void] => {
  const [loading, setLoadingLocal] = React.useState<boolean>(initialState);

  const setLoading = () => {
    setLoadingLocal(true);
    return () => setLoadingLocal(false);
  };

  return [loading, setLoading];
};
```

**设计特点：**
- **函数式设计**：setLoading 返回清理函数
- **自动清理**：通过返回函数实现状态重置
- **类型安全**：完整的 TypeScript 类型推导

### 使用模式

```typescript
// 在 WorkoutRoute.tsx 中的使用
const [workoutLoading, setWorkoutLoading] = useLoading(true);

React.useEffect(() => {
  fetchWorkout(userName, workoutId)
    .then(setWorkout)
    .catch((response) => setError(response.status || true))
    .finally(setWorkoutLoading()); // 自动调用清理函数
}, [workoutId, userName]);
```

**优势分析：**
- **简化异步操作**：一行代码处理加载状态
- **防止内存泄漏**：自动清理机制
- **代码简洁**：减少样板代码

---

## 3. useToggle.tsx - 切换状态管理

### Hook 实现

```typescript
const useToggle = (
  initialState = false,
  onValue = true,
  offValue = false,
): [boolean, () => void, () => void] => {
  const [on, setState] = React.useState(initialState);
  const handleOff = React.useCallback(() => setState(offValue), [offValue]);
  const handleOn = React.useCallback(() => setState(onValue), [onValue]);
  return [on, handleOn, handleOff];
};
```

**设计特点：**
- **三元组返回**：状态值 + 开启函数 + 关闭函数
- **可配置值**：支持自定义开启和关闭值
- **性能优化**：使用 useCallback 缓存函数

### 应用场景

```typescript
// 在 Workout.tsx 中的使用
const [isMenuVisible, openMenu, closeMenu] = useToggle();

// 菜单控制
<Menu open={isMenuVisible} onClose={closeMenu} />
<IconButton onClick={openMenu}>
  <SettingsIcon />
</IconButton>
```

**使用优势：**
- **语义清晰**：明确的开启/关闭操作
- **减少错误**：避免手动状态切换的错误
- **一致性**：统一的切换状态管理模式

---

## 4. usePrevious.tsx - 前值记录

### Hook 实现

```typescript
export function usePrevious<T>(value: T): MutableRefObject<T | undefined>['current'] {
  const ref = React.useRef<T>();
  React.useEffect(() => {
    ref.current = value;
  }, [value]);
  return ref.current;
}
```

**设计特点：**
- **泛型支持**：支持任意类型的值
- **引用存储**：使用 useRef 避免重渲染
- **延迟更新**：useEffect 确保获取的是前一个值

### 典型应用

```typescript
// 检测值的变化
const MyComponent = ({ value }) => {
  const previousValue = usePrevious(value);
  
  React.useEffect(() => {
    if (previousValue !== undefined && previousValue !== value) {
      console.log(`Value changed from ${previousValue} to ${value}`);
    }
  }, [value, previousValue]);
};
```

**应用场景：**
- **变化检测**：监控 props 或 state 的变化
- **动画触发**：基于值变化触发动画
- **调试工具**：跟踪状态变化历史

---

## 5. photos.ts - 照片处理工具

### 核心功能

```typescript
const makeComparator = (
  photoSize: [number, number] | PhotoSize,
): ((psi: PhotoSizeItem) => boolean) => {
  let comparator;
  if (typeof photoSize === 'string') {
    // 字符串尺寸匹配
    comparator = (size: PhotoSizeItem) => size.size === photoSize;
  } else {
    // 数值尺寸匹配
    const [width, height] = photoSize;
    comparator = (size: PhotoSizeItem) => size.width >= width && size.height >= height;
  }
  return comparator;
};

export const getPhotoUrl = (photo: PhotoType, photoSize: [number, number] | PhotoSize): string => {
  const size = [...(photo.sizes || [])].sort(smallerComparator).find(makeComparator(photoSize));
  return size?.url || photo.url;
};
```

**功能特点：**
- **智能尺寸选择**：根据需求选择最合适的照片尺寸
- **降级处理**：找不到合适尺寸时使用原图
- **排序优化**：按尺寸排序确保选择最优解

### 尺寸匹配算法

```mermaid
flowchart TD
    subgraph "输入处理"
        A[photoSize参数] --> B{类型检查}
        B -->|string| C[字符串匹配模式]
        B -->|[number, number]| D[数值匹配模式]
    end
    
    subgraph "尺寸选择"
        E[photo.sizes数组] --> F[按尺寸排序]
        F --> G[应用匹配器]
        C --> G
        D --> G
        G --> H{找到匹配?}
        H -->|是| I[返回匹配URL]
        H -->|否| J[返回原图URL]
    end
```

**算法优势：**
- **性能优化**：排序后查找，避免全量遍历
- **精确匹配**：支持精确尺寸和最小尺寸匹配
- **容错处理**：多层降级确保总有可用图片

---

## 6. noOp.tsx - 空操作函数

### 简洁实现

```typescript
export default (): void => undefined;
```

**设计哲学：**
- **最小实现**：一行代码实现空操作
- **类型明确**：明确返回 void 类型
- **通用性**：可用于任何需要空函数的场景

### 应用场景

```typescript
// usePosition.tsx 中的应用
export const PositionContext = React.createContext({
  position: 0,
  setPosition: noOp,        // 默认空操作
  isPlaying: false,
  setIsPlaying: noOp,       // 默认空操作
  isMapInitialized: false,
  setIsMapInitialized: noOp, // 默认空操作
});

// MeasurementSystemService.tsx 中的应用
React.useEffect(() => {
  loadCulture()
    .then(getPreferredMeasurementSystem)
    .then(setMeasurementSystem)
    .catch(noOp); // 错误静默处理
}, []);
```

**使用价值：**
- **默认值提供**：Context 的默认函数值
- **错误静默**：非关键错误的静默处理
- **接口完整性**：确保接口的完整实现

---

## 7. 工具函数的设计模式

### 1. 函数式编程模式

```typescript
// 高阶函数模式
const makeComparator = (photoSize) => (psi) => /* 比较逻辑 */;

// 柯里化模式
const scale = (input) => (inputRange) => (targetRange) => /* 计算逻辑 */;
```

### 2. Hook 设计模式

```typescript
// 状态 + 操作函数的组合
const useLoading = () => [state, operation];
const useToggle = () => [state, onOperation, offOperation];
```

### 3. 类型安全模式

```typescript
// 泛型约束
function usePrevious<T>(value: T): T | undefined;

// 类型守卫
const hasValue = (name: string, value: any): value is number | string;
```

## 性能优化策略

### 1. 缓存优化

```typescript
// useToggle 中的函数缓存
const handleOff = React.useCallback(() => setState(offValue), [offValue]);
const handleOn = React.useCallback(() => setState(onValue), [onValue]);
```

### 2. 引用稳定性

```typescript
// usePrevious 使用 ref 避免重渲染
const ref = React.useRef<T>();
```

### 3. 计算优化

```typescript
// photos.ts 中的排序优化
const size = [...(photo.sizes || [])].sort(smallerComparator).find(makeComparator(photoSize));
```

## 扩展性设计

### 1. 数学工具扩展

```typescript
// 可以轻松添加新的数学函数
export const clamp = (value: number, min: number, max: number): number => 
  Math.min(Math.max(value, min), max);

export const lerp = (start: number, end: number, t: number): number => 
  start + (end - start) * t;
```

### 2. Hook 扩展

```typescript
// 可以基于现有 Hook 创建复合 Hook
const useAsyncToggle = (asyncOperation) => {
  const [loading, setLoading] = useLoading();
  const [isOn, turnOn, turnOff] = useToggle();
  
  const asyncTurnOn = async () => {
    const cleanup = setLoading();
    try {
      await asyncOperation();
      turnOn();
    } finally {
      cleanup();
    }
  };
  
  return [isOn, asyncTurnOn, turnOff, loading];
};
```

## 总结

### 技术价值
- **基础设施**：为整个应用提供稳定的基础功能
- **代码复用**：避免重复实现常用功能
- **类型安全**：完整的 TypeScript 类型支持

### 工程价值
- **一致性**：统一的工具函数使用模式
- **可维护性**：集中管理常用功能
- **可测试性**：纯函数易于单元测试

### 设计哲学
- **简洁性**：每个函数专注单一职责
- **通用性**：设计为可复用的通用工具
- **可靠性**：经过实际应用验证的稳定实现

这些工具函数虽然代码量不大，但却是整个应用的基石，体现了优秀的软件工程实践和函数式编程思想。
