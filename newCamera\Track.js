/**
 * GPS轨迹处理类
 * 处理GPS轨迹数据，计算边界和观察点
 */
export class Track {
  constructor(points) {
    this.points = points;
    this.bounds = Track.getBounds(points);

    if (points.length > 0) {
      this.tStart = points[0].timestamp;
      this.tEnd = points[points.length - 1].timestamp;
      this.duration = this.tEnd - this.tStart;
    } else {
      this.tStart = 0;
      this.tEnd = 0;
      this.duration = 0;
    }
  }

  /**
   * 获取观察点列表
   * @param {number} numberOfPoints - 点的数量
   * @param {InterpolatedChain} centeringBehaviour - 中心化行为
   * @returns {Array} 观察点数组
   */
  getLookAtPoints(numberOfPoints, centeringBehaviour) {
    if (this.duration === 0) return [];
    
    const step = this.duration / (numberOfPoints - 1);
    const lookAtPoints = [];

    let t = this.tStart;

    this.points.forEach((point) => {
      if (point.timestamp >= t) {
        const progress = (point.timestamp - this.tStart) / this.duration;
        const factor = centeringBehaviour.getValue(progress);

        lookAtPoints.push({
          lat: point.latitude * (1 - factor) + this.bounds.latCenter * factor,
          lon: point.longitude * (1 - factor) + this.bounds.lonCenter * factor,
          alt: point.altitude * (1 - factor) + this.bounds.altCenter * factor,
          t: (t - this.tStart) / this.duration,
        });

        t += step;
      }
    });

    return lookAtPoints;
  }

  /**
   * 计算轨迹点的边界
   * @param {Array} points - 轨迹点数组
   * @returns {Object} 边界信息
   */
  static getBounds(points) {
    if (points.length === 0) {
      return {
        latMin: 0, latMax: 0, latCenter: 0,
        lonMin: 0, lonMax: 0, lonCenter: 0,
        altMin: 0, altMax: 0, altCenter: 0
      };
    }

    let latMin = points[0].latitude;
    let latMax = points[0].latitude;
    let lonMin = points[0].longitude;
    let lonMax = points[0].longitude;
    let altMin = points[0].altitude;
    let altMax = points[0].altitude;

    points.forEach((point) => {
      latMin = Math.min(latMin, point.latitude);
      latMax = Math.max(latMax, point.latitude);
      lonMin = Math.min(lonMin, point.longitude);
      lonMax = Math.max(lonMax, point.longitude);
      altMin = Math.min(altMin, point.altitude);
      altMax = Math.max(altMax, point.altitude);
    });

    const latCenter = (latMin + latMax) / 2;
    const lonCenter = (lonMin + lonMax) / 2;
    const altCenter = (altMin + altMax) / 2;

    return {
      latMin,
      latMax,
      latCenter,
      lonMin,
      lonMax,
      lonCenter,
      altMin,
      altMax,
      altCenter,
    };
  }
}

/**
 * 插值链类
 * 用于在数组中进行插值
 */
export class InterpolatedChain {
  constructor(array) {
    this.array = array;
  }

  /**
   * 获取插值后的值
   * @param {number} t - 参数值 (0-1)
   * @returns {number} 插值结果
   */
  getValue(t) {
    const position = t * (this.array.length - 1);
    const indexFrom = Math.floor(position);
    const indexTo = Math.ceil(position);
    const valueDelta = this.array[indexTo] - this.array[indexFrom];
    const interpolation = position - indexFrom;

    return this.array[indexFrom] + valueDelta * interpolation;
  }
}
