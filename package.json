{"name": "mapboxgl-demo", "version": "1.0.0", "description": "MapboxGL JS 演示项目", "main": "index.html", "scripts": {"start": "python -m http.server 8000", "serve": "python -m http.server 8000", "dev": "python start-server.py", "open": "python -c \"import webbrowser; webbrowser.open('http://localhost:8000')\""}, "keywords": ["mapbox", "mapboxgl", "gis", "maps", "demo", "javascript"], "author": "MapboxGL Demo", "license": "MIT", "repository": {"type": "git", "url": "."}, "dependencies": {}, "devDependencies": {}, "engines": {"python": ">=3.6"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}