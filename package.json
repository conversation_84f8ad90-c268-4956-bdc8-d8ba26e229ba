{"name": "mapboxgl-better", "version": "1.0.0", "description": "高性能MapboxGL地图应用 - 基于Vue3+Vite+TypeScript", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "vue-i18n": "^9.8.0", "mapbox-gl": "^2.15.0", "@mapbox/mapbox-gl-geocoder": "^5.0.1", "axios": "^1.6.2"}, "devDependencies": {"@types/node": "^20.10.0", "@types/mapbox-gl": "^2.7.19", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "typescript": "~5.3.0", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}, "keywords": ["vue3", "vite", "typescript", "mapbox", "webgl", "3d", "gis", "workout", "track", "animation"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=18.0.0"}}