# Mapbox 模块核心功能架构图

本文档使用 Mermaid 图表详细展示 `static/js/Workout/Mapbox` 模块的所有核心功能和架构关系。

## 1. 整体架构概览

```mermaid
graph TB
    subgraph "React 层"
        A[Mapbox.tsx<br/>主地图组件] --> B[CrossFadeOverlay.tsx<br/>淡入淡出效果]
        A --> C[getImageFromMap.ts<br/>截图工具]
    end
    
    subgraph "底层地图引擎"
        D[WorkoutMap.ts<br/>地图核心逻辑]
        E[mapbox-gl<br/>地图渲染引擎]
    end
    
    subgraph "数据层"
        F[Workout<br/>运动数据]
        G[Position Context<br/>位置状态]
        H[MeasurementSystem<br/>测量系统]
    end
    
    A --> D
    D --> E
    A --> F
    A --> G
    A --> H
    B --> C
    B --> D
```

## 2. Mapbox.tsx 组件功能详解

```mermaid
flowchart TD
    subgraph "Mapbox 组件生命周期"
        A1[组件初始化] --> A2[创建 WorkoutMap 实例]
        A2 --> A3[设置事件监听器]
        A3 --> A4[地图渲染完成]
        A4 --> A5[组件销毁时清理资源]
    end
    
    subgraph "状态管理"
        B1[isScreenshotTaken<br/>截图状态]
        B2[isMapInitialized<br/>地图初始化状态]
        B3[isPlaying<br/>播放状态]
        B4[position<br/>当前位置]
        B5[top<br/>视差滚动偏移]
    end
    
    subgraph "用户交互处理"
        C1[鼠标按下] --> C2[checkMapInteraction]
        C3[触摸开始] --> C2
        C4[缩放开始] --> C2
        C2 --> C5[停止播放]
    end
    
    subgraph "视差滚动效果"
        D1[监听 scroll 事件] --> D2[计算偏移量<br/>scrollY * -0.25]
        D2 --> D3[更新 transform]
    end
    
    A4 --> B2
    B3 --> A2
    B4 --> A2
```

## 3. CrossFadeOverlay 淡入淡出机制

```mermaid
sequenceDiagram
    participant User as 用户操作
    participant Overlay as CrossFadeOverlay
    participant Map as WorkoutMap
    participant Canvas as Canvas元素
    participant Screenshot as getImageFromMap
    
    User->>Overlay: 开始播放
    Overlay->>Map: 检查相机位置
    
    alt 相机不在当前位置
        Overlay->>Screenshot: 请求截图
        Screenshot->>Map: 监听 render 事件
        Map->>Screenshot: 触发 render
        Screenshot->>Canvas: 复制地图像素
        Screenshot->>Overlay: 截图完成
        Overlay->>Overlay: 显示 Canvas
        Overlay->>Overlay: 开始淡出动画
        Note over Overlay: 1200ms 淡出过程
        Overlay->>Overlay: 隐藏 Canvas
    else 相机已在当前位置
        Overlay->>Overlay: 直接设置截图完成
    end
    
    Overlay->>User: 更新截图状态
```

## 4. getImageFromMap 截图工具流程

```mermaid
flowchart LR
    subgraph "截图流程"
        A[开始截图] --> B[监听地图 render 事件]
        B --> C[获取地图 Canvas]
        C --> D[设置目标 Canvas 尺寸]
        D --> E[获取 2D 绘图上下文]
        E --> F[使用 drawImage 复制像素]
        F --> G[截图完成]
    end
    
    subgraph "异步处理"
        H[返回 Promise] --> I[等待 render 事件]
        I --> J[执行截图操作]
        J --> K[resolve Promise]
    end
    
    subgraph "渲染触发"
        L[调用 setBearing] --> M[强制触发渲染]
        M --> N[确保截图时机准确]
    end
    
    A --> H
    B --> L
    G --> K
```

## 5. 数据流和状态同步

```mermaid
stateDiagram-v2
    [*] --> 初始化
    初始化 --> 地图创建 : useLayoutEffect
    地图创建 --> 等待初始化 : WorkoutMap 构造
    等待初始化 --> 地图就绪 : setIsMapInitialized(true)
    
    地图就绪 --> 播放状态 : isPlaying 变化
    播放状态 --> 截图检查 : CrossFadeOverlay 激活
    截图检查 --> 截图中 : 需要截图
    截图检查 --> 直接更新 : 无需截图
    截图中 --> 位置更新 : 截图完成
    直接更新 --> 位置更新
    
    位置更新 --> 播放状态 : 继续循环
    
    地图就绪 --> 样式更新 : style 变化
    样式更新 --> 图表重置 : setGraph
    图表重置 --> 地图就绪
    
    地图就绪 --> 图表更新 : graph 变化
    图表更新 --> 地图就绪
    
    地图就绪 --> 销毁 : 组件卸载
    销毁 --> [*] : destructor()
```

## 6. 组件间协作关系

```mermaid
classDiagram
    class Mapbox {
        -mapContainer: RefObject
        -map: WorkoutMap | null
        -top: number
        -isScreenshotTaken: boolean
        +checkMapInteraction(event)
        +render()
    }

    class CrossFadeOverlay {
        -canvasRef: RefObject
        -showCanvas: boolean
        -isFading: boolean
        +DURATION: 1200ms
        +render()
    }

    class getImageFromMap {
        +getImageFromMap(map, canvas): Promise
    }

    class WorkoutMap {
        +mapbox: Map
        +workout: Workout
        +position: number
        +playing: boolean
        +setPosition(position)
        +setPlaying(playing)
        +isCameraAtCurrentPosition()
        +destructor()
    }

    class PositionContext {
        +position: number
        +isPlaying: boolean
        +isMapInitialized: boolean
        +setIsPlaying(boolean)
        +setIsMapInitialized(boolean)
    }

    Mapbox --> WorkoutMap : 创建和管理
    Mapbox --> CrossFadeOverlay : 渲染子组件
    Mapbox --> PositionContext : 使用状态
    CrossFadeOverlay --> getImageFromMap : 调用截图
    CrossFadeOverlay --> WorkoutMap : 检查相机位置
```

## 7. 性能优化策略

```mermaid
graph TB
    subgraph "渲染优化"
        A1[useLayoutEffect] --> A2[避免闪烁]
        A1 --> A3[同步DOM更新]
        B1[条件渲染] --> B2[减少不必要更新]
        B1 --> B3[智能组件显示]
        C1[Canvas复用] --> C2[避免频繁创建]
        C1 --> C3[内存效率]
    end

    subgraph "内存管理"
        D1[事件监听器清理] --> D2[mousedown/touchstart]
        D1 --> D3[zoomstart/scroll]
        E1[WebGL资源释放] --> E2[destructor调用]
        E1 --> E3[地图实例清理]
        F1[状态管理] --> F2[及时重置状态]
        F1 --> F3[避免内存泄漏]
    end

    subgraph "用户体验"
        G1[视差滚动] --> G2[scrollY * -0.25]
        G1 --> G3[沉浸式体验]
        H1[平滑过渡] --> H2[1200ms淡入淡出]
        H1 --> H3[blur效果]
        I1[智能交互] --> I2[自动检测用户操作]
        I1 --> I3[暂停播放]
    end
```

## 8. 错误处理和边界情况

```mermaid
flowchart TD
    subgraph "初始化错误处理"
        A1[mapContainer.current 检查] --> A2{容器是否存在?}
        A2 -->|否| A3[提前返回避免崩溃]
        A2 -->|是| A4[继续初始化]
    end

    subgraph "截图错误处理"
        B1[getImageFromMap 调用] --> B2{获取2D上下文成功?}
        B2 -->|否| B3[Promise reject]
        B2 -->|是| B4[执行截图操作]
        B4 --> B5[Promise resolve]
    end

    subgraph "地图交互边界"
        C1[用户交互事件] --> C2{event.automatic?}
        C2 -->|是| C3[忽略自动事件]
        C2 -->|否| C4[停止播放]
    end

    subgraph "相机位置检查"
        D1[位置更新请求] --> D2{相机在当前位置?}
        D2 -->|是| D3[跳过截图直接更新]
        D2 -->|否| D4[执行截图和淡入淡出]
    end
```

## 9. CSS 样式和动画系统

```mermaid
graph LR
    subgraph "CrossFadeOverlay 样式"
        A1[root 基础样式] --> A2[transition opacity filter]
        A2 --> A3[duration 1200ms]
        A3 --> A4[easing ease-in]

        B1[isFading 状态] --> B2[opacity 0]
        B2 --> B3[filter blur 64px]

        C1[isPresent 状态] --> C2[display block]
        C1 --> C3[z-index 10]
    end

    subgraph "Mapbox 容器样式"
        D1[root 容器] --> D2[transform: translateY]
        D2 --> D3[视差滚动效果]

        E1[crossFade 覆盖层] --> E2[绝对定位]
        E2 --> E3[全屏覆盖]
    end
```

## 10. 事件系统和生命周期

```mermaid
timeline
    title Mapbox 组件生命周期事件

    section 初始化阶段
        组件挂载 : useLayoutEffect 触发
                : 创建 WorkoutMap 实例
                : 注册地图事件监听器

        地图加载 : mapbox 实例创建
                : 样式加载完成
                : setIsMapInitialized(true)

    section 运行阶段
        用户交互 : mousedown/touchstart/zoomstart
                : checkMapInteraction 调用
                : setIsPlaying(false)

        位置更新 : position 状态变化
                : CrossFadeOverlay 响应
                : 截图和淡入淡出处理

        滚动事件 : window scroll 监听
                : 计算视差偏移
                : 更新 transform

    section 清理阶段
        组件卸载 : useLayoutEffect cleanup
                : map.destructor() 调用
                : 移除事件监听器
```

## 11. 数据处理流水线

```mermaid
flowchart LR
    subgraph "输入数据"
        A1[workout: Workout] --> A2[运动轨迹数据]
        A3[graph: string] --> A4[图表类型]
        A5[style: string] --> A6[地图样式]
        A7[position: number] --> A8[当前进度 0-1]
    end

    subgraph "数据转换"
        B1[Workout.getRoute] --> B2[路线点数组]
        B3[measurementSystem] --> B4[单位转换]
        B5[theme] --> B6[样式适配]
    end

    subgraph "渲染输出"
        C1[地图可视化] --> C2[轨迹路线]
        C3[进度标记] --> C4[当前位置]
        C5[数据标签] --> C6[最大/最小值]
        C7[相机动画] --> C8[平滑过渡]
    end

    A2 --> B1
    A4 --> B1
    A6 --> B5
    A8 --> C3
    B2 --> C1
    B4 --> C5
    B6 --> C1
```

## 12. 模块依赖关系

```mermaid
graph TD
    subgraph "外部依赖"
        EXT1[mapbox-gl]
        EXT2[mui-material]
        EXT3[react-i18next]
        EXT4[classnames]
    end

    subgraph "内部模块"
        INT1[WorkoutMap]
        INT2[PositionContext]
        INT3[MeasurementSystemContext]
        INT4[Workout Model]
    end

    subgraph "Mapbox 模块"
        MAP1[Mapbox.tsx]
        MAP2[CrossFadeOverlay.tsx]
        MAP3[getImageFromMap.ts]
    end

    EXT1 --> MAP1
    EXT1 --> MAP3
    EXT2 --> MAP1
    EXT2 --> MAP2
    EXT3 --> MAP1
    EXT4 --> MAP2

    INT1 --> MAP1
    INT2 --> MAP1
    INT3 --> MAP1
    INT4 --> MAP1

    MAP1 --> MAP2
    MAP2 --> MAP3
```

## 13. 核心功能总结

```mermaid
graph TB
    subgraph "主要组件"
        A1[Mapbox.tsx] --> A2[地图容器管理]
        A1 --> A3[生命周期控制]
        A1 --> A4[事件处理]
        A1 --> A5[状态同步]

        B1[CrossFadeOverlay.tsx] --> B2[视觉过渡效果]
        B1 --> B3[截图管理]
        B1 --> B4[动画控制]

        C1[getImageFromMap.ts] --> C2[Canvas 截图]
        C1 --> C3[异步处理]
        C1 --> C4[渲染同步]
    end

    subgraph "核心功能"
        D1[地图渲染] --> D2[Mapbox GL 集成]
        D1 --> D3[样式管理]
        D1 --> D4[交互控制]

        E1[动画系统] --> E2[相机路径]
        E1 --> E3[进度跟踪]
        E1 --> E4[平滑过渡]

        F1[用户体验] --> F2[视差滚动]
        F1 --> F3[淡入淡出]
        F1 --> F4[响应式设计]
    end

    subgraph "技术特性"
        G1[性能优化] --> G2[内存管理]
        G1 --> G3[渲染优化]
        G1 --> G4[事件节流]

        H1[错误处理] --> H2[边界检查]
        H1 --> H3[异常捕获]
        H1 --> H4[优雅降级]

        I1[可维护性] --> I2[模块化设计]
        I1 --> I3[类型安全]
        I1 --> I4[清晰接口]
    end
```

## 14. 使用场景和最佳实践

```mermaid
flowchart TB
    subgraph "使用场景"
        A1[运动轨迹展示] --> A2[GPS 路线可视化]
        A3[数据分析展示] --> A4[心率/速度等指标]
        A5[交互式播放] --> A6[时间轴控制]
    end

    subgraph "最佳实践"
        B1[组件设计] --> B2[单一职责原则]
        B2 --> B3[依赖注入模式]
        B3 --> B4[生命周期管理]

        C1[性能优化] --> C2[避免不必要渲染]
        C2 --> C3[内存泄漏防护]
        C3 --> C4[异步操作处理]

        D1[用户体验] --> D2[平滑动画过渡]
        D2 --> D3[响应式交互]
        D3 --> D4[错误状态处理]
    end

    subgraph "扩展建议"
        E1[功能扩展] --> E2[支持更多地图样式]
        E2 --> E3[增加自定义标记]
        E3 --> E4[多轨迹对比显示]

        F1[性能提升] --> F2[WebGL 优化]
        F2 --> F3[数据预加载]
        F3 --> F4[缓存策略]
    end
```

## 总结

`static/js/Workout/Mapbox` 模块是一个高度集成的地图可视化系统，主要特点包括：

### 🎯 核心价值
- **高性能渲染**: 基于 Mapbox GL JS 的 WebGL 渲染
- **流畅用户体验**: 淡入淡出过渡和视差滚动效果
- **模块化设计**: 清晰的职责分离和接口定义

### 🔧 技术亮点
- **React 集成**: 完整的生命周期管理和状态同步
- **Canvas 截图**: 高效的地图截图和动画处理
- **错误处理**: 完善的边界检查和异常处理机制

### 📈 扩展性
- **样式系统**: 支持多种地图样式和主题
- **数据适配**: 灵活的数据格式和测量系统支持
- **组件复用**: 可在不同场景下复用的组件设计

这个模块展现了现代前端开发中地图可视化的最佳实践，值得作为类似项目的参考架构。
