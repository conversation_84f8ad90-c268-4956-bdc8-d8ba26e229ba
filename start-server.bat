@echo off
chcp 65001 >nul
title MapboxGL Demo Server

echo.
echo 🚀 启动 MapboxGL Demo 服务器...
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到 Python，请先安装 Python 3.x
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "index.html" (
    echo ❌ 缺少 index.html 文件
    pause
    exit /b 1
)

if not exist "app.js" (
    echo ❌ 缺少 app.js 文件
    pause
    exit /b 1
)

if not exist "config.js" (
    echo ❌ 缺少 config.js 文件
    pause
    exit /b 1
)

echo ✅ 文件检查完成
echo 🌐 服务地址: http://localhost:8000
echo 📝 注意: 请确保在 config.js 中设置了有效的 Mapbox Access Token
echo ⏹️  按 Ctrl+C 停止服务器
echo.
echo 正在启动服务器...

REM 启动 Python HTTP 服务器
python -m http.server 8000

pause
