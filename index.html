<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MapboxGL Demo</title>
    
    <!-- MapboxGL CSS -->
    <link href="https://api.mapbox.com/mapbox-gl-js/v2.9.1/mapbox-gl.css" rel="stylesheet">
    
    <!-- MapboxGL Geocoder Plugin CSS -->
    <link rel="stylesheet"
          href="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v5.0.0/mapbox-gl-geocoder.css"
          type="text/css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }

        #map {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
        }

        #controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 100;
            min-width: 200px;
        }

        #controls h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }

        #controls select, #controls button {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }

        #controls button {
            background: #007cbf;
            color: white;
            border: none;
        }

        #controls button:hover {
            background: #005a87;
        }

        #controls button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        #coordinate_info {
            position: absolute;
            bottom: 60px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            z-index: 100;
            font-family: monospace;
        }

        #coordinate_info p {
            margin: 2px 0;
            font-size: 12px;
        }

        .info-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 4px;
            z-index: 100;
            max-width: 300px;
        }
    </style>
</head>

<body>
 
    <div id="controls">
        <h3>地图控制</h3>
        
        <label>地图样式:</label>
        <select id="styleSelect">
            <option value="streets-v11">Streets</option>
            <option value="dark-v10">Dark</option>
            <option value="satellite-streets-v11">Satellite</option>
            <option value="outdoors-v11">Outdoors</option>
        </select>

        <label>快速导航:</label>
        <select id="locationSelect">
            <option value="">选择城市...</option>
            <option value="beijing">北京</option>
            <option value="shanghai">上海</option>
            <option value="guangzhou">广州</option>
            <option value="shenzhen">深圳</option>
        </select>

        <label>自定义瓦片源:</label>
        <select id="customTileSelect">
            <option value="">选择瓦片源...</option>
            <option value="mapbox-satellite-v4">Mapbox 卫星 (v4)</option>
            <option value="osm">OpenStreetMap</option>
            <option value="amap-satellite">高德卫星图</option>
        </select>

        <button id="toggle3D">开启/关闭3D地形</button>

        <div id="terrainControls" style="display: none; margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
            <label>地形夸张系数: <span id="exaggerationValue">1.5</span></label>
            <input type="range" id="exaggerationSlider" min="0.5" max="3.0" step="0.1" value="1.5" style="width: 100%; margin: 5px 0;">

            <label>视角倾斜: <span id="pitchValue">60</span>°</label>
            <input type="range" id="pitchSlider" min="0" max="85" step="5" value="60" style="width: 100%; margin: 5px 0;">
        </div>

        <button id="toggleRotation">开始/停止旋转</button>
        <button id="resetView">重置视图</button>
    </div>

    <div id="coordinate_info">
        <p>经度: <span id="longitude">--</span></p>
        <p>纬度: <span id="latitude">--</span></p>
        <p>缩放: <span id="zoom">--</span></p>
    </div>

    <div id='map'></div>

    <!-- MapboxGL JS -->
    <script src="https://api.mapbox.com/mapbox-gl-js/v2.9.1/mapbox-gl.js"></script>
    
    <!-- MapboxGL Geocoder Plugin JS -->
    <script src="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v5.0.0/mapbox-gl-geocoder.min.js"></script>

    <!-- 配置文件 -->
    <script src="config.js"></script>
    <script src="app.js"></script>
</body>

</html>
