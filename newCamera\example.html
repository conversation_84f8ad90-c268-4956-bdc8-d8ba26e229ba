<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>newCamera 示例</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .info-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #28a745;
            transition: width 0.1s ease;
            width: 0%;
        }
        
        .data-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .data-item {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
        }
        
        .data-item h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
        }
        
        .data-item .value {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
        }
        
        .log {
            background: #343a40;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        
        .file-input {
            margin-bottom: 20px;
        }
        
        .file-input input[type="file"] {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>newCamera 3D相机路径生成库示例</h1>
        
        <div class="file-input">
            <input type="file" id="gpxFile" accept=".gpx" />
            <button onclick="loadGPXFile()">加载GPX文件</button>
            <button onclick="loadDefaultGPX()">加载默认轨迹</button>
        </div>
        
        <div class="controls">
            <button id="playBtn" onclick="togglePlay()" disabled>播放</button>
            <button onclick="stop()" disabled id="stopBtn">停止</button>
            <button onclick="seekToStart()" disabled id="seekStartBtn">跳到开始</button>
            <button onclick="seekToMiddle()" disabled id="seekMiddleBtn">跳到中间</button>
            <button onclick="seekToEnd()" disabled id="seekEndBtn">跳到结束</button>
        </div>
        
        <div class="info-panel">
            <h3>动画状态</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="statusText">未加载轨迹数据</div>
        </div>
        
        <div class="data-display" id="dataDisplay">
            <!-- 数据将在这里动态显示 -->
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script type="module">
        import { CameraAnimationController, GPXParser } from './index.js';
        
        let controller = null;
        
        // 日志函数
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // 更新UI状态
        function updateUI(state) {
            const playBtn = document.getElementById('playBtn');
            const stopBtn = document.getElementById('stopBtn');
            const seekStartBtn = document.getElementById('seekStartBtn');
            const seekMiddleBtn = document.getElementById('seekMiddleBtn');
            const seekEndBtn = document.getElementById('seekEndBtn');
            const progressFill = document.getElementById('progressFill');
            const statusText = document.getElementById('statusText');
            
            // 更新按钮状态
            const hasPath = state.hasPath;
            playBtn.disabled = !hasPath;
            stopBtn.disabled = !hasPath;
            seekStartBtn.disabled = !hasPath;
            seekMiddleBtn.disabled = !hasPath;
            seekEndBtn.disabled = !hasPath;
            
            // 更新播放按钮文本
            playBtn.textContent = state.isPlaying ? '暂停' : '播放';
            
            // 更新进度条
            progressFill.style.width = `${state.progress * 100}%`;
            
            // 更新状态文本
            const minutes = Math.floor(state.currentTime / 60000);
            const seconds = Math.floor((state.currentTime % 60000) / 1000);
            const totalMinutes = Math.floor(state.duration / 60000);
            const totalSeconds = Math.floor((state.duration % 60000) / 1000);
            
            statusText.textContent = hasPath 
                ? `${state.isPlaying ? '播放中' : '已暂停'} - ${minutes}:${seconds.toString().padStart(2, '0')} / ${totalMinutes}:${totalSeconds.toString().padStart(2, '0')}`
                : '未加载轨迹数据';
        }
        
        // 更新数据显示
        function updateDataDisplay(animationState) {
            const dataDisplay = document.getElementById('dataDisplay');
            
            if (!animationState.lookAt) {
                dataDisplay.innerHTML = '<div class="data-item"><h4>等待数据...</h4></div>';
                return;
            }
            
            dataDisplay.innerHTML = `
                <div class="data-item">
                    <h4>当前进度</h4>
                    <div class="value">${(animationState.progress * 100).toFixed(1)}%</div>
                </div>
                <div class="data-item">
                    <h4>缓动进度</h4>
                    <div class="value">${(animationState.easedProgress * 100).toFixed(1)}%</div>
                </div>
                <div class="data-item">
                    <h4>观察点纬度</h4>
                    <div class="value">${animationState.lookAt.lat.toFixed(6)}°</div>
                </div>
                <div class="data-item">
                    <h4>观察点经度</h4>
                    <div class="value">${animationState.lookAt.lon.toFixed(6)}°</div>
                </div>
                <div class="data-item">
                    <h4>观察点高度</h4>
                    <div class="value">${animationState.lookAt.alt.toFixed(1)}m</div>
                </div>
                <div class="data-item">
                    <h4>当前时间</h4>
                    <div class="value">${(animationState.currentTime / 1000).toFixed(1)}s</div>
                </div>
            `;
        }
        
        // 创建控制器
        function createController() {
            controller = new CameraAnimationController({
                duration: 30000, // 30秒
                onUpdate: (animationState) => {
                    updateDataDisplay(animationState);
                    updateUI(controller.getState());
                },
                onComplete: () => {
                    log('动画播放完成');
                    updateUI(controller.getState());
                }
            });
        }
        
        // 加载默认GPX文件
        window.loadDefaultGPX = async function() {
            try {
                log('开始加载默认GPX文件...');
                createController();
                
                const result = await controller.initializeFromGPXURL('../workout_track.gpx');
                log(`GPX文件加载成功，包含 ${result.pointCount} 个轨迹点`);
                
                updateUI(controller.getState());
                updateDataDisplay({ lookAt: null });
                
            } catch (error) {
                log(`加载失败: ${error.message}`);
            }
        };
        
        // 加载GPX文件
        window.loadGPXFile = async function() {
            const fileInput = document.getElementById('gpxFile');
            const file = fileInput.files[0];
            
            if (!file) {
                log('请选择一个GPX文件');
                return;
            }
            
            try {
                log(`开始解析文件: ${file.name}`);
                createController();
                
                const trackPoints = await GPXParser.parseGPXFile(file);
                const validation = GPXParser.validateTrackPoints(trackPoints);
                
                if (!validation.isValid) {
                    throw new Error('GPX数据验证失败: ' + validation.errors.join(', '));
                }
                
                if (validation.warnings.length > 0) {
                    validation.warnings.forEach(warning => log(`警告: ${warning}`));
                }
                
                controller.initializeFromTrackPoints(trackPoints);
                log(`文件解析成功，包含 ${trackPoints.length} 个轨迹点`);
                
                updateUI(controller.getState());
                updateDataDisplay({ lookAt: null });
                
            } catch (error) {
                log(`文件解析失败: ${error.message}`);
            }
        };
        
        // 播放/暂停切换
        window.togglePlay = function() {
            if (!controller) return;
            
            if (controller.getState().isPlaying) {
                controller.pause();
                log('动画已暂停');
            } else {
                controller.play();
                log('动画开始播放');
            }
        };
        
        // 停止
        window.stop = function() {
            if (!controller) return;
            
            controller.stop();
            log('动画已停止');
            updateUI(controller.getState());
        };
        
        // 跳转函数
        window.seekToStart = function() {
            if (!controller) return;
            controller.seekToProgress(0);
            log('跳转到开始');
        };
        
        window.seekToMiddle = function() {
            if (!controller) return;
            controller.seekToProgress(0.5);
            log('跳转到中间');
        };
        
        window.seekToEnd = function() {
            if (!controller) return;
            controller.seekToProgress(1);
            log('跳转到结束');
        };
        
        // 初始化
        log('newCamera 示例已加载');
        updateUI({ hasPath: false, isPlaying: false, progress: 0, currentTime: 0, duration: 30000 });
    </script>
</body>
</html>
