/**
 * 地图状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { MapState, MapStyle, TerrainConfig, SkyConfig } from '@/types/map'
import type { LngLat } from '@/types/common'

export const useMapStore = defineStore('map', () => {
  // 基础状态
  const currentStyle = ref<MapStyle>('streets' as MapStyle)
  const center = ref<LngLat>({ lng: 116.4074, lat: 39.9042 }) // 北京
  const zoom = ref(10)
  const pitch = ref(0)
  const bearing = ref(0)
  const isLoaded = ref(false)
  const isMoving = ref(false)
  const isZooming = ref(false)
  const isRotating = ref(false)

  // 3D功能状态
  const is3DEnabled = ref(false)
  const terrainConfig = ref<TerrainConfig | null>(null)
  const skyConfig = ref<SkyConfig | null>(null)
  const terrainExaggeration = ref(1.5)

  // 控件状态
  const showControls = ref(true)
  const showScale = ref(true)
  const showCompass = ref(true)
  const showFullscreen = ref(true)
  const showGeolocate = ref(true)

  // 动画状态
  const isAnimating = ref(false)
  const animationSpeed = ref(1.0)

  // 计算属性
  const mapState = computed<MapState>(() => ({
    center: center.value,
    zoom: zoom.value,
    pitch: pitch.value,
    bearing: bearing.value,
    bounds: {
      north: center.value.lat + 0.1,
      south: center.value.lat - 0.1,
      east: center.value.lng + 0.1,
      west: center.value.lng - 0.1
    },
    isLoaded: isLoaded.value,
    isMoving: isMoving.value,
    isZooming: isZooming.value,
    isRotating: isRotating.value
  }))

  const is3DMode = computed(() => is3DEnabled.value && pitch.value > 0)

  // Actions
  const setStyle = (style: MapStyle) => {
    currentStyle.value = style
    console.log(`🎨 地图样式切换为: ${style}`)
  }

  const setCenter = (newCenter: LngLat) => {
    center.value = newCenter
  }

  const setZoom = (newZoom: number) => {
    zoom.value = Math.max(0, Math.min(24, newZoom))
  }

  const setPitch = (newPitch: number) => {
    pitch.value = Math.max(0, Math.min(85, newPitch))
  }

  const setBearing = (newBearing: number) => {
    bearing.value = ((newBearing % 360) + 360) % 360
  }

  const updateState = (state: Partial<MapState>) => {
    if (state.center) center.value = state.center
    if (state.zoom !== undefined) zoom.value = state.zoom
    if (state.pitch !== undefined) pitch.value = state.pitch
    if (state.bearing !== undefined) bearing.value = state.bearing
    if (state.isLoaded !== undefined) isLoaded.value = state.isLoaded
    if (state.isMoving !== undefined) isMoving.value = state.isMoving
    if (state.isZooming !== undefined) isZooming.value = state.isZooming
    if (state.isRotating !== undefined) isRotating.value = state.isRotating
  }

  const enable3D = () => {
    is3DEnabled.value = true
    if (pitch.value === 0) {
      setPitch(45)
    }
    console.log('🏔️ 3D模式已启用')
  }

  const disable3D = () => {
    is3DEnabled.value = false
    setPitch(0)
    terrainConfig.value = null
    skyConfig.value = null
    console.log('🗺️ 3D模式已禁用')
  }

  const setTerrainConfig = (config: TerrainConfig | null) => {
    terrainConfig.value = config
    if (config) {
      terrainExaggeration.value = config.exaggeration
      console.log(`⛰️ 地形配置已更新: 夸张系数 ${config.exaggeration}`)
    }
  }

  const setSkyConfig = (config: SkyConfig | null) => {
    skyConfig.value = config
    if (config) {
      console.log('☁️ 天空配置已更新')
    }
  }

  const setTerrainExaggeration = (exaggeration: number) => {
    terrainExaggeration.value = Math.max(0.1, Math.min(5.0, exaggeration))
    if (terrainConfig.value) {
      terrainConfig.value.exaggeration = terrainExaggeration.value
    }
  }

  const toggleControls = () => {
    showControls.value = !showControls.value
  }

  const setControlVisibility = (control: string, visible: boolean) => {
    switch (control) {
      case 'scale':
        showScale.value = visible
        break
      case 'compass':
        showCompass.value = visible
        break
      case 'fullscreen':
        showFullscreen.value = visible
        break
      case 'geolocate':
        showGeolocate.value = visible
        break
    }
  }

  const startAnimation = () => {
    isAnimating.value = true
    console.log('▶️ 动画已开始')
  }

  const stopAnimation = () => {
    isAnimating.value = false
    console.log('⏹️ 动画已停止')
  }

  const setAnimationSpeed = (speed: number) => {
    animationSpeed.value = Math.max(0.1, Math.min(5.0, speed))
  }

  const resetView = () => {
    setCenter({ lng: 116.4074, lat: 39.9042 })
    setZoom(10)
    setPitch(0)
    setBearing(0)
    stopAnimation()
    console.log('🔄 视图已重置')
  }

  // 预设位置
  const flyToBeijing = () => {
    setCenter({ lng: 116.4074, lat: 39.9042 })
    setZoom(12)
  }

  const flyToShanghai = () => {
    setCenter({ lng: 121.4737, lat: 31.2304 })
    setZoom(12)
  }

  const flyToShenzhen = () => {
    setCenter({ lng: 114.0579, lat: 22.5431 })
    setZoom(12)
  }

  return {
    // State
    currentStyle,
    center,
    zoom,
    pitch,
    bearing,
    isLoaded,
    isMoving,
    isZooming,
    isRotating,
    is3DEnabled,
    terrainConfig,
    skyConfig,
    terrainExaggeration,
    showControls,
    showScale,
    showCompass,
    showFullscreen,
    showGeolocate,
    isAnimating,
    animationSpeed,

    // Computed
    mapState,
    is3DMode,

    // Actions
    setStyle,
    setCenter,
    setZoom,
    setPitch,
    setBearing,
    updateState,
    enable3D,
    disable3D,
    setTerrainConfig,
    setSkyConfig,
    setTerrainExaggeration,
    toggleControls,
    setControlVisibility,
    startAnimation,
    stopAnimation,
    setAnimationSpeed,
    resetView,
    flyToBeijing,
    flyToShanghai,
    flyToShenzhen
  }
})
