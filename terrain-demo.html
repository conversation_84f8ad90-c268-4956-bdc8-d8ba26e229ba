<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MapboxGL 3D地形演示</title>
    
    <!-- MapboxGL CSS -->
    <link href="https://api.mapbox.com/mapbox-gl-js/v3.14.0/mapbox-gl.css" rel="stylesheet">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }

        #map {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
        }

        #controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 100;
            min-width: 280px;
            max-height: 80vh;
            overflow-y: auto;
        }

        #controls h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
            border-bottom: 2px solid #007cbf;
            padding-bottom: 5px;
        }

        .control-group {
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .control-group input[type="range"] {
            width: 100%;
            margin: 5px 0;
        }

        .control-group select, .control-group button {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }

        .control-group button {
            background: #007cbf;
            color: white;
            border: none;
            font-weight: bold;
        }

        .control-group button:hover {
            background: #005a87;
        }

        .control-group button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .value-display {
            display: inline-block;
            background: #007cbf;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-weight: bold;
            min-width: 40px;
            text-align: center;
        }

        #info-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 4px;
            z-index: 100;
            font-family: monospace;
            font-size: 12px;
        }

        #info-panel h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }

        #info-panel p {
            margin: 3px 0;
        }

        .preset-locations {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            margin-top: 10px;
        }

        .preset-locations button {
            padding: 6px;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div id="controls">
        <h3>🏔️ 3D地形控制</h3>
        
        <div class="control-group">
            <label>地形夸张系数: <span class="value-display" id="exaggerationValue">1.5</span></label>
            <input type="range" id="exaggerationSlider" min="0" max="5" step="0.1" value="1.5">
        </div>

        <div class="control-group">
            <label>视角倾斜: <span class="value-display" id="pitchValue">80</span>°</label>
            <input type="range" id="pitchSlider" min="0" max="85" step="1" value="80">
        </div>

        <div class="control-group">
            <label>视角方位: <span class="value-display" id="bearingValue">41</span>°</label>
            <input type="range" id="bearingSlider" min="0" max="360" step="1" value="41">
        </div>

        <div class="control-group">
            <label>缩放级别: <span class="value-display" id="zoomValue">14</span></label>
            <input type="range" id="zoomSlider" min="8" max="18" step="0.1" value="14">
        </div>

        <div class="control-group">
            <label>地图样式:</label>
            <select id="styleSelect">
                <option value="standard-satellite">Standard Satellite</option>
                <option value="satellite-streets-v11">Satellite Streets</option>
                <option value="outdoors-v11">Outdoors</option>
                <option value="streets-v11">Streets</option>
            </select>
        </div>

        <div class="control-group">
            <label>预设地点:</label>
            <div class="preset-locations">
                <button onclick="flyToLocation([-114.26608, 32.7213], '大峡谷')">大峡谷</button>
                <button onclick="flyToLocation([86.9250, 27.9881], '珠穆朗玛峰')">珠峰</button>
                <button onclick="flyToLocation([-119.5383, 37.7161], '优胜美地')">优胜美地</button>
                <button onclick="flyToLocation([7.6581, 45.9763], '勃朗峰')">勃朗峰</button>
                <button onclick="flyToLocation([103.8198, 1.3521], '新加坡')">新加坡</button>
                <button onclick="flyToLocation([139.6917, 35.6895], '东京')">东京</button>
            </div>
        </div>

        <div class="control-group">
            <button id="toggleAnimation">开始自动旋转</button>
            <button id="resetView">重置视图</button>
        </div>
    </div>

    <div id="info-panel">
        <h4>📍 当前位置信息</h4>
        <p>经度: <span id="longitude">--</span></p>
        <p>纬度: <span id="latitude">--</span></p>
        <p>海拔: <span id="elevation">--</span>m</p>
        <p>缩放: <span id="currentZoom">--</span></p>
    </div>

    <div id='map'></div>

    <!-- MapboxGL JS -->
    <script src="https://api.mapbox.com/mapbox-gl-js/v3.14.0/mapbox-gl.js"></script>
    
    <!-- 配置文件 -->
    <script src="config.js"></script>
    
    <script>
        // 使用配置文件中的 Access Token
        mapboxgl.accessToken = CONFIG.MAPBOX_ACCESS_TOKEN;

        // 初始化地图
        const map = new mapboxgl.Map({
            container: 'map',
            zoom: 14,
            center: [-114.26608, 32.7213], // 大峡谷
            pitch: 80,
            bearing: 41,
            style: 'mapbox://styles/mapbox/standard-satellite'
        });

        let animationId = null;
        let isAnimating = false;

        // 地图加载完成后添加地形
        map.on('style.load', () => {
            // 添加DEM数据源
            map.addSource('mapbox-dem', {
                'type': 'raster-dem',
                'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
                'tileSize': 512,
                'maxzoom': 14
            });

            // 设置地形
            map.setTerrain({ 
                'source': 'mapbox-dem', 
                'exaggeration': 1.5 
            });

            // 添加天空层
            map.addLayer({
                'id': 'sky',
                'type': 'sky',
                'paint': {
                    'sky-type': 'atmosphere',
                    'sky-atmosphere-sun': [0.0, 0.0],
                    'sky-atmosphere-sun-intensity': 15
                }
            });

            console.log('3D地形已加载');
        });

        // 更新坐标信息
        function updateInfo() {
            const center = map.getCenter();
            const zoom = map.getZoom();
            const pitch = map.getPitch();
            const bearing = map.getBearing();

            document.getElementById('longitude').textContent = center.lng.toFixed(6);
            document.getElementById('latitude').textContent = center.lat.toFixed(6);
            document.getElementById('currentZoom').textContent = zoom.toFixed(2);
            
            // 更新滑块值
            document.getElementById('zoomValue').textContent = zoom.toFixed(1);
            document.getElementById('zoomSlider').value = zoom;
            document.getElementById('pitchValue').textContent = Math.round(pitch);
            document.getElementById('pitchSlider').value = pitch;
            document.getElementById('bearingValue').textContent = Math.round(bearing);
            document.getElementById('bearingSlider').value = bearing;
        }

        // 地图移动时更新信息
        map.on('move', updateInfo);
        map.on('load', updateInfo);

        // 控制事件监听器
        document.getElementById('exaggerationSlider').addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            document.getElementById('exaggerationValue').textContent = value;
            if (map.getTerrain()) {
                map.setTerrain({ 'source': 'mapbox-dem', 'exaggeration': value });
            }
        });

        document.getElementById('pitchSlider').addEventListener('input', (e) => {
            const value = parseInt(e.target.value);
            document.getElementById('pitchValue').textContent = value;
            map.setPitch(value);
        });

        document.getElementById('bearingSlider').addEventListener('input', (e) => {
            const value = parseInt(e.target.value);
            document.getElementById('bearingValue').textContent = value;
            map.setBearing(value);
        });

        document.getElementById('zoomSlider').addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            document.getElementById('zoomValue').textContent = value.toFixed(1);
            map.setZoom(value);
        });

        document.getElementById('styleSelect').addEventListener('change', (e) => {
            map.setStyle(`mapbox://styles/mapbox/${e.target.value}`);
        });

        // 飞行到指定位置
        function flyToLocation(coordinates, name) {
            map.flyTo({
                center: coordinates,
                zoom: 14,
                pitch: 80,
                bearing: 41,
                speed: 0.8,
                curve: 1.42
            });
            console.log(`飞行到: ${name}`);
        }

        // 自动旋转动画
        function startAnimation() {
            isAnimating = true;
            document.getElementById('toggleAnimation').textContent = '停止自动旋转';
            
            function rotate() {
                if (!isAnimating) return;
                const bearing = map.getBearing();
                map.setBearing(bearing + 0.2);
                animationId = requestAnimationFrame(rotate);
            }
            
            animationId = requestAnimationFrame(rotate);
        }

        function stopAnimation() {
            isAnimating = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            document.getElementById('toggleAnimation').textContent = '开始自动旋转';
        }

        document.getElementById('toggleAnimation').addEventListener('click', () => {
            if (isAnimating) {
                stopAnimation();
            } else {
                startAnimation();
            }
        });

        document.getElementById('resetView').addEventListener('click', () => {
            stopAnimation();
            map.flyTo({
                center: [-114.26608, 32.7213],
                zoom: 14,
                pitch: 80,
                bearing: 41,
                speed: 1.2
            });
        });

        // 初始化信息显示
        setTimeout(updateInfo, 1000);
    </script>
</body>

</html>
