<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D运动轨迹动画演示</title>
    
    <!-- MapboxGL CSS -->
    <link href="https://api.mapbox.com/mapbox-gl-js/v3.14.0/mapbox-gl.css" rel="stylesheet">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
        }

        #map {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
        }

        #controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
            z-index: 100;
            min-width: 300px;
            backdrop-filter: blur(10px);
        }

        #controls h3 {
            margin: 0 0 15px 0;
            color: #4CAF50;
            font-size: 18px;
            text-align: center;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 8px;
        }

        .control-group {
            margin-bottom: 15px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #E0E0E0;
        }

        .control-group input[type="range"] {
            width: 100%;
            margin: 8px 0;
            accent-color: #4CAF50;
        }

        .control-group button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: none;
            border-radius: 6px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .control-group button:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
        }

        .control-group button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .value-display {
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 4px 10px;
            border-radius: 4px;
            font-weight: bold;
            min-width: 50px;
            text-align: center;
            font-family: monospace;
        }

        #info-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 100;
            font-family: monospace;
            font-size: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        #info-panel h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
            font-size: 14px;
        }

        #info-panel p {
            margin: 3px 0;
            color: #E0E0E0;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.3s ease;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }

        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #4CAF50;
        }

        .stat-label {
            font-size: 10px;
            color: #B0B0B0;
            margin-top: 2px;
        }
    </style>
</head>

<body>
    <div id="controls">
        <h3>🏃‍♂️ 运动轨迹动画</h3>
        
        <div class="control-group">
            <label>动画速度: <span class="value-display" id="speedValue">1.0</span>x</label>
            <input type="range" id="speedSlider" min="0.1" max="5.0" step="0.1" value="1.0">
        </div>

        <div class="control-group">
            <label>地形夸张: <span class="value-display" id="exaggerationValue">2.0</span></label>
            <input type="range" id="exaggerationSlider" min="0.5" max="5.0" step="0.1" value="2.0">
        </div>

        <div class="control-group">
            <label>相机高度: <span class="value-display" id="cameraHeightValue">200</span>m</label>
            <input type="range" id="cameraHeightSlider" min="50" max="500" step="10" value="200">
        </div>

        <div class="control-group">
            <button id="startAnimation">▶️ 开始动画</button>
            <button id="pauseAnimation" disabled>⏸️ 暂停动画</button>
            <button id="resetAnimation">🔄 重置动画</button>
        </div>

        <div class="control-group">
            <label>动画进度</label>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <input type="range" id="progressSlider" min="0" max="100" step="0.1" value="0">
        </div>

        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value" id="currentPoint">0</div>
                <div class="stat-label">当前点</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="totalPoints">0</div>
                <div class="stat-label">总点数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="currentElevation">0</div>
                <div class="stat-label">海拔(m)</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="totalDistance">0</div>
                <div class="stat-label">距离(km)</div>
            </div>
        </div>
    </div>

    <div id="info-panel">
        <h4>📍 实时位置信息</h4>
        <p>经度: <span id="longitude">--</span></p>
        <p>纬度: <span id="latitude">--</span></p>
        <p>海拔: <span id="elevation">--</span>m</p>
        <p>时间: <span id="timestamp">--</span></p>
        <p>速度: <span id="speed">--</span> km/h</p>
    </div>

    <div id='map'></div>

    <!-- MapboxGL JS -->
    <script src="https://api.mapbox.com/mapbox-gl-js/v3.14.0/mapbox-gl.js"></script>
    
    <!-- 配置文件 -->
    <script src="config.js"></script>
    
    <script>
        // 使用配置文件中的 Access Token
        mapboxgl.accessToken = CONFIG.MAPBOX_ACCESS_TOKEN;

        // 全局变量
        let map;
        let trackData = [];
        let animationId = null;
        let isAnimating = false;
        let currentPointIndex = 0;
        let animationSpeed = 1.0;
        let cameraRotationAngle = 0;

        // 初始化地图
        function initMap() {
            map = new mapboxgl.Map({
                container: 'map',
                style: 'mapbox://styles/mapbox/satellite-streets-v11',
                center: [10.3090512, 43.9034624], // GPX轨迹的起始点
                zoom: 15,
                pitch: 70,
                bearing: 0,
                antialias: true
            });

            // 地图加载完成后的处理
            map.on('style.load', () => {
                setupTerrain();
                loadTrackData();
            });
        }

        // 设置3D地形
        function setupTerrain() {
            // 添加DEM数据源
            map.addSource('mapbox-dem', {
                'type': 'raster-dem',
                'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
                'tileSize': 512,
                'maxzoom': 14
            });

            // 设置地形
            map.setTerrain({
                'source': 'mapbox-dem',
                'exaggeration': 2.0
            });

            // 添加天空层
            map.addLayer({
                'id': 'sky',
                'type': 'sky',
                'paint': {
                    'sky-type': 'atmosphere',
                    'sky-atmosphere-sun': [0.0, 90.0],
                    'sky-atmosphere-sun-intensity': 15
                }
            });

            console.log('3D地形已设置');
        }

        // 加载GPX轨迹数据
        async function loadTrackData() {
            try {
                const response = await fetch('workout_track.gpx');
                const gpxText = await response.text();
                
                // 解析GPX数据
                const parser = new DOMParser();
                const gpxDoc = parser.parseFromString(gpxText, 'text/xml');
                const trackPoints = gpxDoc.querySelectorAll('trkpt');
                
                trackData = Array.from(trackPoints).map((point, index) => {
                    const lat = parseFloat(point.getAttribute('lat'));
                    const lon = parseFloat(point.getAttribute('lon'));
                    const ele = parseFloat(point.querySelector('ele')?.textContent || '0');
                    const time = point.querySelector('time')?.textContent || '';
                    
                    return {
                        coordinates: [lon, lat],
                        elevation: ele,
                        timestamp: time,
                        index: index
                    };
                });

                console.log(`已加载 ${trackData.length} 个轨迹点`);
                
                // 更新UI
                document.getElementById('totalPoints').textContent = trackData.length;
                
                // 计算总距离
                const totalDistance = calculateTotalDistance();
                document.getElementById('totalDistance').textContent = totalDistance.toFixed(2);
                
                // 创建轨迹线
                createTrackLine();
                
                // 设置地图视图到轨迹范围
                fitMapToTrack();
                
            } catch (error) {
                console.error('加载GPX数据失败:', error);
                alert('无法加载GPX文件，请确保文件存在');
            }
        }

        // 计算总距离
        function calculateTotalDistance() {
            let totalDistance = 0;
            for (let i = 1; i < trackData.length; i++) {
                const prev = trackData[i - 1];
                const curr = trackData[i];
                totalDistance += calculateDistance(
                    prev.coordinates[1], prev.coordinates[0],
                    curr.coordinates[1], curr.coordinates[0]
                );
            }
            return totalDistance;
        }

        // 计算两点间距离（公里）
        function calculateDistance(lat1, lon1, lat2, lon2) {
            const R = 6371; // 地球半径（公里）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLon = (lon2 - lon1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                    Math.sin(dLon/2) * Math.sin(dLon/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // 创建轨迹线
        function createTrackLine() {
            // 完整轨迹线（灰色）
            map.addSource('full-track', {
                'type': 'geojson',
                'data': {
                    'type': 'Feature',
                    'properties': {},
                    'geometry': {
                        'type': 'LineString',
                        'coordinates': trackData.map(point => point.coordinates)
                    }
                }
            });

            map.addLayer({
                'id': 'full-track-line',
                'type': 'line',
                'source': 'full-track',
                'layout': {
                    'line-join': 'round',
                    'line-cap': 'round'
                },
                'paint': {
                    'line-color': '#888888',
                    'line-width': 3,
                    'line-opacity': 0.6
                }
            });

            // 已走过的轨迹线（绿色）
            map.addSource('animated-track', {
                'type': 'geojson',
                'data': {
                    'type': 'Feature',
                    'properties': {},
                    'geometry': {
                        'type': 'LineString',
                        'coordinates': []
                    }
                }
            });

            map.addLayer({
                'id': 'animated-track-line',
                'type': 'line',
                'source': 'animated-track',
                'layout': {
                    'line-join': 'round',
                    'line-cap': 'round'
                },
                'paint': {
                    'line-color': '#4CAF50',
                    'line-width': 5,
                    'line-opacity': 0.9
                }
            });

            // 当前位置点
            map.addSource('current-position', {
                'type': 'geojson',
                'data': {
                    'type': 'Feature',
                    'properties': {},
                    'geometry': {
                        'type': 'Point',
                        'coordinates': trackData[0].coordinates
                    }
                }
            });

            map.addLayer({
                'id': 'current-position-point',
                'type': 'circle',
                'source': 'current-position',
                'paint': {
                    'circle-radius': 8,
                    'circle-color': '#FF5722',
                    'circle-stroke-color': '#FFFFFF',
                    'circle-stroke-width': 3
                }
            });
        }

        // 设置地图视图到轨迹范围
        function fitMapToTrack() {
            const coordinates = trackData.map(point => point.coordinates);
            const bounds = coordinates.reduce((bounds, coord) => {
                return bounds.extend(coord);
            }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]));

            map.fitBounds(bounds, {
                padding: 50,
                pitch: 70,
                bearing: 0
            });
        }

        // 开始动画
        function startAnimation() {
            if (trackData.length === 0) return;
            
            isAnimating = true;
            document.getElementById('startAnimation').disabled = true;
            document.getElementById('pauseAnimation').disabled = false;
            
            animate();
        }

        // 暂停动画
        function pauseAnimation() {
            isAnimating = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            
            document.getElementById('startAnimation').disabled = false;
            document.getElementById('pauseAnimation').disabled = true;
        }

        // 重置动画
        function resetAnimation() {
            pauseAnimation();
            currentPointIndex = 0;
            cameraRotationAngle = 0;
            updateTrackVisualization();
            updateUI();
        }

        // 动画循环
        function animate() {
            // 检查是否到达终点
            if (!isAnimating || currentPointIndex >= trackData.length - 1) {
                // 轨迹绘制完成，移动相机到终点正上方
                if (currentPointIndex >= trackData.length - 1 && trackData.length > 0) {
                    moveToEndpoint();
                }
                pauseAnimation();
                return;
            }

            // 更新当前点
            currentPointIndex += animationSpeed;
            if (currentPointIndex >= trackData.length) {
                currentPointIndex = trackData.length - 1;
            }

            // 更新可视化
            updateTrackVisualization();
            updateCamera();
            updateUI();

            animationId = requestAnimationFrame(animate);
        }

        // 移动相机到终点正上方
        function moveToEndpoint() {
            if (trackData.length === 0) return;

            const endPoint = trackData[trackData.length - 1];

            // 使用flyTo平滑移动到终点正上方
            map.flyTo({
                center: endPoint.coordinates,
                zoom: 17,
                pitch: 85, // 接近正上方的俯视角度
                bearing: 0, // 重置旋转角度
                duration: 2000, // 2秒的飞行时间
                essential: true
            });

            console.log('相机已移动到终点正上方');
        }

        // 更新轨迹可视化
        function updateTrackVisualization() {
            const currentIndex = Math.floor(currentPointIndex);
            if (currentIndex >= trackData.length) return;

            // 更新已走过的轨迹
            const animatedCoordinates = trackData.slice(0, currentIndex + 1).map(point => point.coordinates);
            map.getSource('animated-track').setData({
                'type': 'Feature',
                'properties': {},
                'geometry': {
                    'type': 'LineString',
                    'coordinates': animatedCoordinates
                }
            });

            // 更新当前位置
            map.getSource('current-position').setData({
                'type': 'Feature',
                'properties': {},
                'geometry': {
                    'type': 'Point',
                    'coordinates': trackData[currentIndex].coordinates
                }
            });
        }

        // 更新相机位置
        function updateCamera() {
            const currentIndex = Math.floor(currentPointIndex);
            if (currentIndex >= trackData.length) return;

            const currentPoint = trackData[currentIndex];
            const cameraHeight = parseInt(document.getElementById('cameraHeightSlider').value);
            
            // 相机旋转
            cameraRotationAngle += 0.5;
            
            map.easeTo({
                center: currentPoint.coordinates,
                zoom: 16,
                pitch: 75,
                bearing: cameraRotationAngle % 360,
                duration: 100
            });
        }

        // 更新UI
        function updateUI() {
            const currentIndex = Math.floor(currentPointIndex);
            if (currentIndex >= trackData.length) return;

            const currentPoint = trackData[currentIndex];
            const progress = (currentPointIndex / (trackData.length - 1)) * 100;

            // 更新进度
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressSlider').value = progress;

            // 更新统计信息
            document.getElementById('currentPoint').textContent = currentIndex + 1;
            document.getElementById('currentElevation').textContent = Math.round(currentPoint.elevation);

            // 更新位置信息
            document.getElementById('longitude').textContent = currentPoint.coordinates[0].toFixed(6);
            document.getElementById('latitude').textContent = currentPoint.coordinates[1].toFixed(6);
            document.getElementById('elevation').textContent = Math.round(currentPoint.elevation);
            document.getElementById('timestamp').textContent = new Date(currentPoint.timestamp).toLocaleTimeString();

            // 计算速度
            if (currentIndex > 0) {
                const prevPoint = trackData[currentIndex - 1];
                const distance = calculateDistance(
                    prevPoint.coordinates[1], prevPoint.coordinates[0],
                    currentPoint.coordinates[1], currentPoint.coordinates[0]
                );
                const timeDiff = (new Date(currentPoint.timestamp) - new Date(prevPoint.timestamp)) / 1000 / 3600; // 小时
                const speed = timeDiff > 0 ? (distance / timeDiff) : 0;
                document.getElementById('speed').textContent = speed.toFixed(1);
            }
        }

        // 事件监听器
        function setupEventListeners() {
            // 速度控制
            document.getElementById('speedSlider').addEventListener('input', (e) => {
                animationSpeed = parseFloat(e.target.value);
                document.getElementById('speedValue').textContent = animationSpeed.toFixed(1);
            });

            // 地形夸张控制
            document.getElementById('exaggerationSlider').addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                document.getElementById('exaggerationValue').textContent = value.toFixed(1);
                if (map.getTerrain()) {
                    map.setTerrain({ 'source': 'mapbox-dem', 'exaggeration': value });
                }
            });

            // 相机高度控制
            document.getElementById('cameraHeightSlider').addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                document.getElementById('cameraHeightValue').textContent = value;
            });

            // 进度控制
            document.getElementById('progressSlider').addEventListener('input', (e) => {
                const progress = parseFloat(e.target.value);
                currentPointIndex = (progress / 100) * (trackData.length - 1);
                updateTrackVisualization();
                updateCamera();
                updateUI();
            });

            // 按钮控制
            document.getElementById('startAnimation').addEventListener('click', startAnimation);
            document.getElementById('pauseAnimation').addEventListener('click', pauseAnimation);
            document.getElementById('resetAnimation').addEventListener('click', resetAnimation);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initMap();
            setupEventListeners();
        });
    </script>
</body>

</html>
