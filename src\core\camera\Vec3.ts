/**
 * 3D向量数学库
 * 提供基本的3D向量运算功能
 */
export class Vec3 {
  public x: number
  public y: number
  public z: number

  constructor(x: number, y: number, z: number) {
    this.x = x
    this.y = y
    this.z = z
  }

  /**
   * 向量乘以标量
   * @param value - 标量值
   * @returns 新的向量
   */
  multiply(value: number): Vec3 {
    return new Vec3(this.x * value, this.y * value, this.z * value)
  }

  /**
   * 向量除以标量
   * @param value - 标量值
   * @returns 新的向量
   */
  divide(value: number): Vec3 {
    if (value === 0) {
      throw new Error('Division by zero')
    }
    return new Vec3(this.x / value, this.y / value, this.z / value)
  }

  /**
   * 向量加法
   * @param vec - 另一个向量
   * @returns 新的向量
   */
  add(vec: Vec3): Vec3 {
    return new Vec3(this.x + vec.x, this.y + vec.y, this.z + vec.z)
  }

  /**
   * 向量减法
   * @param vec - 另一个向量
   * @returns 新的向量
   */
  subtract(vec: Vec3): Vec3 {
    return new Vec3(this.x - vec.x, this.y - vec.y, this.z - vec.z)
  }

  /**
   * 计算向量长度
   * @returns 向量长度
   */
  length(): number {
    return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z)
  }

  /**
   * 向量归一化
   * @returns 归一化后的向量
   */
  normalize(): Vec3 {
    const length = this.length()
    return length === 0 ? new Vec3(0, 0, 0) : this.multiply(1 / length)
  }

  /**
   * 计算到另一个向量的距离
   * @param vec - 另一个向量
   * @returns 距离
   */
  distance(vec: Vec3): number {
    const dx = vec.x - this.x
    const dy = vec.y - this.y
    const dz = vec.z - this.z
    return Math.sqrt(dx * dx + dy * dy + dz * dz)
  }

  /**
   * 向量点积
   * @param vec - 另一个向量
   * @returns 点积结果
   */
  dot(vec: Vec3): number {
    return this.x * vec.x + this.y * vec.y + this.z * vec.z
  }

  /**
   * 向量叉积
   * @param vec - 另一个向量
   * @returns 叉积结果向量
   */
  cross(vec: Vec3): Vec3 {
    return new Vec3(
      this.y * vec.z - this.z * vec.y,
      this.z * vec.x - this.x * vec.z,
      this.x * vec.y - this.y * vec.x
    )
  }

  /**
   * 线性插值
   * @param vec - 目标向量
   * @param t - 插值参数 (0-1)
   * @returns 插值结果向量
   */
  lerp(vec: Vec3, t: number): Vec3 {
    const clampedT = Math.max(0, Math.min(1, t))
    return this.add(vec.subtract(this).multiply(clampedT))
  }

  /**
   * 复制向量
   * @returns 新的向量副本
   */
  clone(): Vec3 {
    return new Vec3(this.x, this.y, this.z)
  }

  /**
   * 转换为数组
   * @returns [x, y, z]
   */
  toArray(): [number, number, number] {
    return [this.x, this.y, this.z]
  }

  /**
   * 转换为字符串
   * @returns 字符串表示
   */
  toString(): string {
    return `Vec3(${this.x.toFixed(3)}, ${this.y.toFixed(3)}, ${this.z.toFixed(3)})`
  }

  /**
   * 检查向量是否相等
   * @param vec - 另一个向量
   * @param epsilon - 误差范围
   * @returns 是否相等
   */
  equals(vec: Vec3, epsilon = 1e-6): boolean {
    return (
      Math.abs(this.x - vec.x) < epsilon &&
      Math.abs(this.y - vec.y) < epsilon &&
      Math.abs(this.z - vec.z) < epsilon
    )
  }

  /**
   * 创建零向量
   * @returns 零向量
   */
  static zero(): Vec3 {
    return new Vec3(0, 0, 0)
  }

  /**
   * 创建单位向量
   * @returns 单位向量
   */
  static one(): Vec3 {
    return new Vec3(1, 1, 1)
  }

  /**
   * 创建X轴单位向量
   * @returns X轴单位向量
   */
  static unitX(): Vec3 {
    return new Vec3(1, 0, 0)
  }

  /**
   * 创建Y轴单位向量
   * @returns Y轴单位向量
   */
  static unitY(): Vec3 {
    return new Vec3(0, 1, 0)
  }

  /**
   * 创建Z轴单位向量
   * @returns Z轴单位向量
   */
  static unitZ(): Vec3 {
    return new Vec3(0, 0, 1)
  }

  /**
   * 从数组创建向量
   * @param array - 数组 [x, y, z]
   * @returns 新的向量
   */
  static fromArray(array: [number, number, number]): Vec3 {
    return new Vec3(array[0], array[1], array[2])
  }
}

/**
 * 角度转弧度
 * @param deg - 角度
 * @returns 弧度
 */
export function radians(deg: number): number {
  return deg * (Math.PI / 180)
}

/**
 * 弧度转角度
 * @param rad - 弧度
 * @returns 角度
 */
export function degrees(rad: number): number {
  return rad * (180 / Math.PI)
}

/**
 * 限制数值在指定范围内
 * @param value - 数值
 * @param min - 最小值
 * @param max - 最大值
 * @returns 限制后的数值
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.max(min, Math.min(max, value))
}

/**
 * 线性插值
 * @param a - 起始值
 * @param b - 结束值
 * @param t - 插值参数 (0-1)
 * @returns 插值结果
 */
export function lerp(a: number, b: number, t: number): number {
  return a + (b - a) * clamp(t, 0, 1)
}
