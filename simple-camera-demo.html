<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>newCamera 简单演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .demo-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        
        .status {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-weight: bold;
            color: #1976d2;
        }
        
        .success { background: #e8f5e8; border-color: #4caf50; color: #2e7d32; }
        .error { background: #ffebee; border-color: #f44336; color: #c62828; }
        
        button {
            background: linear-gradient(45deg, #2196f3, #21cbf3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .data-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .data-card h4 {
            margin: 0 0 10px 0;
            color: #666;
            font-size: 14px;
        }
        
        .data-card .value {
            font-size: 18px;
            font-weight: bold;
            color: #2196f3;
        }
        
        .progress-container {
            margin: 20px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 25px;
            background: #e0e0e0;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #4caf50, #8bc34a);
            transition: width 0.2s ease;
            width: 0%;
            border-radius: 15px;
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #333;
            font-weight: bold;
            font-size: 12px;
        }
        
        .visualization {
            background: #1a1a1a;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
        }
        
        .track-info {
            background: linear-gradient(45deg, #ff9800, #ff5722);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .track-info h3 {
            margin: 0 0 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 newCamera 3D相机路径演示</h1>
        
        <div id="status" class="status">
            🚀 准备就绪 - 点击"开始演示"加载GPX轨迹数据
        </div>
        
        <div class="controls">
            <button id="startBtn" onclick="startDemo()">🎯 开始演示</button>
            <button id="playBtn" onclick="togglePlay()" disabled>▶️ 播放动画</button>
            <button id="stopBtn" onclick="stopAnimation()" disabled>⏹️ 停止</button>
            <button id="resetBtn" onclick="resetDemo()" disabled>🔄 重置</button>
        </div>
        
        <div class="demo-section">
            <h3>📊 实时动画数据</h3>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                    <div class="progress-text" id="progressText">0%</div>
                </div>
            </div>
            
            <div class="data-grid">
                <div class="data-card">
                    <h4>⏱️ 当前时间</h4>
                    <div class="value" id="currentTime">0.0s</div>
                </div>
                <div class="data-card">
                    <h4>⏳ 总时长</h4>
                    <div class="value" id="totalTime">30.0s</div>
                </div>
                <div class="data-card">
                    <h4>🌍 纬度</h4>
                    <div class="value" id="latitude">-</div>
                </div>
                <div class="data-card">
                    <h4>🌍 经度</h4>
                    <div class="value" id="longitude">-</div>
                </div>
                <div class="data-card">
                    <h4>⛰️ 高度</h4>
                    <div class="value" id="altitude">-</div>
                </div>
                <div class="data-card">
                    <h4>📍 轨迹点数</h4>
                    <div class="value" id="pointCount">-</div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🎮 快速跳转</h3>
            <div class="controls">
                <button onclick="seekTo(0)" disabled class="seek-btn">⏮️ 起点</button>
                <button onclick="seekTo(0.2)" disabled class="seek-btn">20%</button>
                <button onclick="seekTo(0.4)" disabled class="seek-btn">40%</button>
                <button onclick="seekTo(0.6)" disabled class="seek-btn">60%</button>
                <button onclick="seekTo(0.8)" disabled class="seek-btn">80%</button>
                <button onclick="seekTo(1)" disabled class="seek-btn">⏭️ 终点</button>
            </div>
        </div>
        
        <div id="trackInfo" class="track-info" style="display: none;">
            <h3>📍 轨迹信息</h3>
            <div id="trackDetails"></div>
        </div>
        
        <div class="visualization" id="console">
            <div>🎬 newCamera 3D相机路径生成库</div>
            <div>📁 等待加载GPX轨迹数据...</div>
        </div>
    </div>

    <script type="module">
        import { CameraAnimationController, GPXParser } from './newCamera/index.js';
        
        let controller = null;
        let isInitialized = false;
        
        // 控制台日志
        function log(message, type = 'info') {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : '📝';
            console.innerHTML += `<div>[${timestamp}] ${icon} ${message}</div>`;
            console.scrollTop = console.scrollHeight;
        }
        
        // 更新状态
        function updateStatus(message, type = '') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        // 更新UI
        function updateUI(state) {
            // 更新进度
            const progress = (state.progress * 100).toFixed(1);
            document.getElementById('progressFill').style.width = `${progress}%`;
            document.getElementById('progressText').textContent = `${progress}%`;
            
            // 更新时间
            document.getElementById('currentTime').textContent = `${(state.currentTime / 1000).toFixed(1)}s`;
            document.getElementById('totalTime').textContent = `${(state.duration / 1000).toFixed(1)}s`;
            
            // 更新按钮状态
            const hasPath = state.hasPath;
            document.getElementById('playBtn').disabled = !hasPath;
            document.getElementById('stopBtn').disabled = !hasPath;
            document.getElementById('resetBtn').disabled = !hasPath;
            
            // 更新跳转按钮
            document.querySelectorAll('.seek-btn').forEach(btn => {
                btn.disabled = !hasPath;
            });
            
            // 更新播放按钮文本
            const playBtn = document.getElementById('playBtn');
            playBtn.textContent = state.isPlaying ? '⏸️ 暂停' : '▶️ 播放动画';
        }
        
        // 更新相机数据
        function updateCameraData(animationState) {
            if (animationState.lookAt) {
                document.getElementById('latitude').textContent = animationState.lookAt.lat.toFixed(6) + '°';
                document.getElementById('longitude').textContent = animationState.lookAt.lon.toFixed(6) + '°';
                document.getElementById('altitude').textContent = animationState.lookAt.alt.toFixed(1) + 'm';
            }
        }
        
        // 显示轨迹信息
        function showTrackInfo(bounds, pointCount) {
            const trackInfo = document.getElementById('trackInfo');
            const trackDetails = document.getElementById('trackDetails');
            
            trackDetails.innerHTML = `
                <div><strong>📍 轨迹点数:</strong> ${pointCount} 个</div>
                <div><strong>🌍 纬度范围:</strong> ${bounds.latMin.toFixed(6)}° ~ ${bounds.latMax.toFixed(6)}°</div>
                <div><strong>🌍 经度范围:</strong> ${bounds.lonMin.toFixed(6)}° ~ ${bounds.lonMax.toFixed(6)}°</div>
                <div><strong>⛰️ 高度范围:</strong> ${bounds.altMin.toFixed(1)}m ~ ${bounds.altMax.toFixed(1)}m</div>
                <div><strong>📍 中心点:</strong> ${bounds.latCenter.toFixed(6)}°, ${bounds.lonCenter.toFixed(6)}°</div>
            `;
            
            trackInfo.style.display = 'block';
        }
        
        // 开始演示
        window.startDemo = async function() {
            try {
                updateStatus('🔄 正在加载GPX轨迹数据...', '');
                log('开始加载 workout_track.gpx 文件...');
                
                // 创建控制器
                controller = new CameraAnimationController({
                    duration: 30000, // 30秒动画
                    onUpdate: (animationState) => {
                        updateCameraData(animationState);
                        updateUI(controller.getState());
                    },
                    onComplete: () => {
                        updateStatus('🎉 动画播放完成！', 'success');
                        log('动画播放完成', 'success');
                        updateUI(controller.getState());
                    }
                });
                
                // 加载GPX数据
                const result = await controller.initializeFromGPXURL('./workout_track.gpx');
                
                updateStatus(`✅ 成功加载轨迹数据 (${result.pointCount} 个点)`, 'success');
                log(`GPX文件解析成功，包含 ${result.pointCount} 个轨迹点`, 'success');
                
                // 显示轨迹信息
                if (controller.cameraPath && controller.cameraPath.bounds) {
                    showTrackInfo(controller.cameraPath.bounds, result.pointCount);
                }
                
                document.getElementById('pointCount').textContent = result.pointCount;
                
                // 更新UI
                updateUI(controller.getState());
                isInitialized = true;
                
                // 禁用开始按钮
                document.getElementById('startBtn').disabled = true;
                
                log('🎬 相机路径已生成，可以开始播放动画');
                
            } catch (error) {
                console.error('演示初始化失败:', error);
                updateStatus(`❌ 加载失败: ${error.message}`, 'error');
                log(`加载失败: ${error.message}`, 'error');
            }
        };
        
        // 播放/暂停
        window.togglePlay = function() {
            if (!controller || !isInitialized) return;
            
            const state = controller.getState();
            if (state.isPlaying) {
                controller.pause();
                updateStatus('⏸️ 动画已暂停', '');
                log('动画暂停');
            } else {
                controller.play();
                updateStatus('▶️ 动画播放中...', '');
                log('开始播放动画');
            }
        };
        
        // 停止动画
        window.stopAnimation = function() {
            if (!controller) return;
            
            controller.stop();
            updateStatus('⏹️ 动画已停止', '');
            log('动画停止');
            updateUI(controller.getState());
        };
        
        // 重置演示
        window.resetDemo = function() {
            if (!controller) return;
            
            controller.stop();
            updateStatus('🔄 演示已重置', '');
            log('演示重置');
            updateUI(controller.getState());
        };
        
        // 跳转到指定位置
        window.seekTo = function(progress) {
            if (!controller) return;
            
            controller.seekToProgress(progress);
            updateStatus(`⏭️ 跳转到 ${(progress * 100).toFixed(0)}%`, '');
            log(`跳转到 ${(progress * 100).toFixed(0)}% 位置`);
        };
        
        // 初始化
        log('newCamera 3D相机路径生成库已加载');
        log('点击"开始演示"按钮加载GPX轨迹数据');
        updateUI({ hasPath: false, isPlaying: false, progress: 0, currentTime: 0, duration: 30000 });
    </script>
</body>
</html>
