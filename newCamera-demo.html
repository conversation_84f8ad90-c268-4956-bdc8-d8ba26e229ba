<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>newCamera + Mapbox GL JS 演示</title>
    
    <!-- Mapbox GL JS -->
    <script src="https://api.mapbox.com/mapbox-gl-js/v3.14.0/mapbox-gl.js"></script>
    <link href="https://api.mapbox.com/mapbox-gl-js/v3.14.0/mapbox-gl.css" rel="stylesheet">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
        }

        #map {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
        }

        #controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 100;
            min-width: 300px;
        }

        #controls h3 {
            margin: 0 0 15px 0;
            color: #4CAF50;
            text-align: center;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
        }

        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
            font-size: 14px;
        }

        button:hover {
            background: #45a049;
        }

        button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: #4CAF50;
            transition: width 0.1s ease;
            width: 0%;
        }

        .info-display {
            background: #2a2a2a;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .info-value {
            color: #4CAF50;
            font-weight: bold;
        }

        #status {
            color: #FFC107;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
        }

        .loading {
            text-align: center;
            color: #FFC107;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    
    <div id="controls">
        <h3>🎬 newCamera 演示</h3>
        
        <div id="status">正在加载GPX数据...</div>
        
        <div class="control-group">
            <button id="playBtn" onclick="togglePlay()" disabled>▶️ 播放</button>
            <button id="stopBtn" onclick="stop()" disabled>⏹️ 停止</button>
            <button id="resetBtn" onclick="reset()" disabled>🔄 重置</button>
        </div>
        
        <div class="control-group">
            <label>动画进度</label>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
        
        <div class="control-group">
            <button onclick="seekTo(0)">⏮️ 开始</button>
            <button onclick="seekTo(0.25)">25%</button>
            <button onclick="seekTo(0.5)">50%</button>
            <button onclick="seekTo(0.75)">75%</button>
            <button onclick="seekTo(1)">⏭️ 结束</button>
        </div>
        
        <div class="info-display">
            <div class="info-row">
                <span>当前时间:</span>
                <span class="info-value" id="currentTime">0.0s</span>
            </div>
            <div class="info-row">
                <span>总时长:</span>
                <span class="info-value" id="totalTime">30.0s</span>
            </div>
            <div class="info-row">
                <span>观察点纬度:</span>
                <span class="info-value" id="lookAtLat">-</span>
            </div>
            <div class="info-row">
                <span>观察点经度:</span>
                <span class="info-value" id="lookAtLon">-</span>
            </div>
            <div class="info-row">
                <span>观察点高度:</span>
                <span class="info-value" id="lookAtAlt">-</span>
            </div>
            <div class="info-row">
                <span>轨迹点数:</span>
                <span class="info-value" id="pointCount">-</span>
            </div>
        </div>
    </div>

    <script type="module">
        import { CameraAnimationController, GPXParser } from './newCamera/index.js';
        
        // Mapbox访问令牌 - 请替换为你的令牌
        mapboxgl.accessToken = 'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw';
        
        let map;
        let controller;
        let trackData = [];
        
        // 初始化地图
        function initMap() {
            map = new mapboxgl.Map({
                container: 'map',
                style: 'mapbox://styles/mapbox/satellite-streets-v12',
                center: [10.309, 43.903], // 意大利托斯卡纳地区
                zoom: 14,
                pitch: 60,
                bearing: 0
            });
            
            map.on('load', () => {
                console.log('地图加载完成');
                initializeCamera();
            });
        }
        
        // 初始化相机控制器
        async function initializeCamera() {
            try {
                updateStatus('正在解析GPX文件...');
                
                // 创建相机控制器
                controller = new CameraAnimationController({
                    duration: 30000, // 30秒动画
                    onUpdate: (state) => {
                        updateCameraPosition(state);
                        updateUI(state);
                    },
                    onComplete: () => {
                        updateStatus('动画播放完成');
                        document.getElementById('playBtn').textContent = '▶️ 播放';
                        document.getElementById('playBtn').disabled = false;
                    }
                });
                
                // 从GPX文件加载数据
                const result = await controller.initializeFromGPXURL('./workout_track.gpx');
                
                // 获取轨迹数据用于地图显示
                trackData = await GPXParser.loadGPXFromURL('./workout_track.gpx');
                
                updateStatus(`已加载 ${result.pointCount} 个轨迹点`);
                document.getElementById('pointCount').textContent = result.pointCount;
                
                // 启用控制按钮
                enableControls();
                
                // 添加轨迹线到地图
                addTrackToMap();
                
                // 设置初始相机位置
                fitMapToTrack();
                
            } catch (error) {
                console.error('初始化失败:', error);
                updateStatus('加载失败: ' + error.message);
            }
        }
        
        // 更新相机位置
        function updateCameraPosition(state) {
            if (!state.lookAt || !map) return;
            
            const { lookAt } = state;
            
            // 计算相机高度和角度
            const baseAltitude = lookAt.alt || 0;
            const cameraHeight = baseAltitude + 200 + Math.sin(state.progress * Math.PI * 2) * 50;
            const bearing = state.progress * 360; // 旋转一圈
            
            // 平滑移动相机
            map.easeTo({
                center: [lookAt.lon, lookAt.lat],
                zoom: 16,
                bearing: bearing,
                pitch: 65,
                duration: 100
            });
        }
        
        // 添加轨迹线到地图
        function addTrackToMap() {
            if (trackData.length === 0) return;
            
            // 创建轨迹线数据
            const lineString = {
                type: 'Feature',
                properties: {},
                geometry: {
                    type: 'LineString',
                    coordinates: trackData.map(point => [point.longitude, point.latitude])
                }
            };
            
            // 添加轨迹线源
            map.addSource('track-line', {
                type: 'geojson',
                data: lineString
            });
            
            // 添加轨迹线图层
            map.addLayer({
                id: 'track-line-layer',
                type: 'line',
                source: 'track-line',
                layout: {
                    'line-join': 'round',
                    'line-cap': 'round'
                },
                paint: {
                    'line-color': '#FF5722',
                    'line-width': 4,
                    'line-opacity': 0.8
                }
            });
            
            // 添加起点标记
            map.addSource('start-point', {
                type: 'geojson',
                data: {
                    type: 'Feature',
                    properties: { title: '起点' },
                    geometry: {
                        type: 'Point',
                        coordinates: [trackData[0].longitude, trackData[0].latitude]
                    }
                }
            });
            
            map.addLayer({
                id: 'start-point-layer',
                type: 'circle',
                source: 'start-point',
                paint: {
                    'circle-radius': 8,
                    'circle-color': '#4CAF50',
                    'circle-stroke-color': '#FFFFFF',
                    'circle-stroke-width': 2
                }
            });
            
            // 添加终点标记
            const endIndex = trackData.length - 1;
            map.addSource('end-point', {
                type: 'geojson',
                data: {
                    type: 'Feature',
                    properties: { title: '终点' },
                    geometry: {
                        type: 'Point',
                        coordinates: [trackData[endIndex].longitude, trackData[endIndex].latitude]
                    }
                }
            });
            
            map.addLayer({
                id: 'end-point-layer',
                type: 'circle',
                source: 'end-point',
                paint: {
                    'circle-radius': 8,
                    'circle-color': '#F44336',
                    'circle-stroke-color': '#FFFFFF',
                    'circle-stroke-width': 2
                }
            });
        }
        
        // 适配地图视图到轨迹
        function fitMapToTrack() {
            if (trackData.length === 0) return;

            const coordinates = trackData.map(point => [point.longitude, point.latitude]);
            const bounds = coordinates.reduce((bounds, coord) => {
                return bounds.extend(coord);
            }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]));

            map.fitBounds(bounds, {
                padding: 100,
                pitch: 60,
                bearing: 0
            });
        }

        // 更新状态显示
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        // 启用控制按钮
        function enableControls() {
            document.getElementById('playBtn').disabled = false;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('resetBtn').disabled = false;
        }

        // 更新UI显示
        function updateUI(state) {
            // 更新进度条
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = `${state.progress * 100}%`;

            // 更新时间显示
            document.getElementById('currentTime').textContent = `${(state.currentTime / 1000).toFixed(1)}s`;
            document.getElementById('totalTime').textContent = `${(state.duration / 1000).toFixed(1)}s`;

            // 更新观察点信息
            if (state.lookAt) {
                document.getElementById('lookAtLat').textContent = state.lookAt.lat.toFixed(6) + '°';
                document.getElementById('lookAtLon').textContent = state.lookAt.lon.toFixed(6) + '°';
                document.getElementById('lookAtAlt').textContent = state.lookAt.alt.toFixed(1) + 'm';
            }
        }

        // 全局控制函数
        window.togglePlay = function() {
            if (!controller) return;

            const state = controller.getState();
            const playBtn = document.getElementById('playBtn');

            if (state.isPlaying) {
                controller.pause();
                playBtn.textContent = '▶️ 播放';
                updateStatus('动画已暂停');
            } else {
                controller.play();
                playBtn.textContent = '⏸️ 暂停';
                updateStatus('动画播放中...');
            }
        };

        window.stop = function() {
            if (!controller) return;

            controller.stop();
            document.getElementById('playBtn').textContent = '▶️ 播放';
            updateStatus('动画已停止');

            // 重置地图视图
            fitMapToTrack();
        };

        window.reset = function() {
            if (!controller) return;

            controller.stop();
            document.getElementById('playBtn').textContent = '▶️ 播放';
            updateStatus('动画已重置');

            // 重置地图视图
            fitMapToTrack();
        };

        window.seekTo = function(progress) {
            if (!controller) return;

            controller.seekToProgress(progress);
            updateStatus(`跳转到 ${(progress * 100).toFixed(0)}%`);
        };

        // 初始化应用
        initMap();
    </script>
</body>
</html>
