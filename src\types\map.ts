/**
 * 地图相关类型定义
 */

import type { Map as MapboxMap, LngLatLike, StyleSpecification } from 'mapbox-gl'
import type { LngLat, Point, Bounds } from './common'

// 地图配置
export interface MapConfig {
  container: string | HTMLElement
  style: string | StyleSpecification
  center: LngLatLike
  zoom: number
  pitch: number
  bearing: number
  minZoom?: number
  maxZoom?: number
  maxBounds?: Bounds
  accessToken: string
}

// 地图状态
export interface MapState {
  center: LngLat
  zoom: number
  pitch: number
  bearing: number
  bounds: Bounds
  isLoaded: boolean
  isMoving: boolean
  isZooming: boolean
  isRotating: boolean
}

// 地图控件类型
export enum MapControlType {
  NAVIGATION = 'navigation',
  SCALE = 'scale',
  FULLSCREEN = 'fullscreen',
  GEOLOCATE = 'geolocate',
  GEOCODER = 'geocoder',
  ATTRIBUTION = 'attribution'
}

// 地图控件配置
export interface MapControlConfig {
  type: MapControlType
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  options?: any
}

// 地图图层类型
export enum LayerType {
  FILL = 'fill',
  LINE = 'line',
  SYMBOL = 'symbol',
  CIRCLE = 'circle',
  HEATMAP = 'heatmap',
  FILL_EXTRUSION = 'fill-extrusion',
  RASTER = 'raster',
  HILLSHADE = 'hillshade',
  BACKGROUND = 'background',
  SKY = 'sky'
}

// 图层配置
export interface LayerConfig {
  id: string
  type: LayerType
  source: string
  sourceLayer?: string
  layout?: any
  paint?: any
  filter?: any
  minzoom?: number
  maxzoom?: number
}

// 数据源类型
export enum SourceType {
  VECTOR = 'vector',
  RASTER = 'raster',
  RASTER_DEM = 'raster-dem',
  GEOJSON = 'geojson',
  IMAGE = 'image',
  VIDEO = 'video',
  CANVAS = 'canvas'
}

// 数据源配置
export interface SourceConfig {
  type: SourceType
  url?: string
  tiles?: string[]
  data?: any
  scheme?: 'xyz' | 'tms'
  tileSize?: number
  attribution?: string
  bounds?: number[]
  minzoom?: number
  maxzoom?: number
}

// 地形配置
export interface TerrainConfig {
  source: string
  exaggeration: number
}

// 天空配置
export interface SkyConfig {
  'sky-type': 'gradient' | 'atmosphere'
  'sky-atmosphere-sun'?: [number, number]
  'sky-atmosphere-halo-color'?: string
  'sky-atmosphere-color'?: string
  'sky-atmosphere-sun-intensity'?: number
}

// 地图事件类型
export interface MapEventData {
  lngLat: LngLat
  point: Point
  originalEvent?: Event
}

// 地图引擎接口
export interface IMapEngine {
  map: MapboxMap | null
  isInitialized: boolean
  
  initialize(config: MapConfig): Promise<void>
  destroy(): void
  
  // 视图控制
  flyTo(center: LngLatLike, zoom?: number, options?: any): void
  jumpTo(center: LngLatLike, zoom?: number, options?: any): void
  fitBounds(bounds: Bounds, options?: any): void
  
  // 样式管理
  setStyle(style: string | StyleSpecification): void
  getStyle(): StyleSpecification | undefined
  
  // 图层管理
  addLayer(layer: LayerConfig): void
  removeLayer(layerId: string): void
  updateLayer(layerId: string, updates: Partial<LayerConfig>): void
  
  // 数据源管理
  addSource(sourceId: string, source: SourceConfig): void
  removeSource(sourceId: string): void
  updateSource(sourceId: string, data: any): void
  
  // 3D功能
  enableTerrain(config: TerrainConfig): void
  disableTerrain(): void
  setSky(config: SkyConfig): void
  
  // 事件处理
  on(event: string, handler: (data: MapEventData) => void): void
  off(event: string, handler: (data: MapEventData) => void): void
}
