<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>newCamera 测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
        }
        
        .status {
            background: #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-weight: bold;
        }
        
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .data-display {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .data-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #28a745;
            transition: width 0.1s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 newCamera 库测试</h1>
        
        <div id="status" class="status info">正在初始化...</div>
        
        <div class="controls">
            <button id="loadBtn" onclick="loadGPX()">📁 加载GPX数据</button>
            <button id="playBtn" onclick="togglePlay()" disabled>▶️ 播放</button>
            <button id="stopBtn" onclick="stop()" disabled>⏹️ 停止</button>
        </div>
        
        <div class="data-display">
            <h3>📊 动画数据</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="data-row">
                <span>进度:</span>
                <span id="progress">0%</span>
            </div>
            <div class="data-row">
                <span>当前时间:</span>
                <span id="currentTime">0.0s</span>
            </div>
            <div class="data-row">
                <span>总时长:</span>
                <span id="totalTime">30.0s</span>
            </div>
            <div class="data-row">
                <span>观察点纬度:</span>
                <span id="lookAtLat">-</span>
            </div>
            <div class="data-row">
                <span>观察点经度:</span>
                <span id="lookAtLon">-</span>
            </div>
            <div class="data-row">
                <span>观察点高度:</span>
                <span id="lookAtAlt">-</span>
            </div>
            <div class="data-row">
                <span>轨迹点数:</span>
                <span id="pointCount">-</span>
            </div>
        </div>
        
        <div class="controls">
            <button onclick="seekTo(0)" disabled id="seek0">⏮️ 0%</button>
            <button onclick="seekTo(0.25)" disabled id="seek25">25%</button>
            <button onclick="seekTo(0.5)" disabled id="seek50">50%</button>
            <button onclick="seekTo(0.75)" disabled id="seek75">75%</button>
            <button onclick="seekTo(1)" disabled id="seek100">⏭️ 100%</button>
        </div>
    </div>

    <script type="module">
        import { CameraAnimationController, GPXParser } from './newCamera/index.js';
        
        let controller = null;
        
        // 更新状态显示
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        // 更新UI
        function updateUI(state) {
            // 更新进度条
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = `${state.progress * 100}%`;
            
            // 更新数据显示
            document.getElementById('progress').textContent = `${(state.progress * 100).toFixed(1)}%`;
            document.getElementById('currentTime').textContent = `${(state.currentTime / 1000).toFixed(1)}s`;
            document.getElementById('totalTime').textContent = `${(state.duration / 1000).toFixed(1)}s`;
            
            // 更新按钮状态
            const playBtn = document.getElementById('playBtn');
            playBtn.textContent = state.isPlaying ? '⏸️ 暂停' : '▶️ 播放';
            
            // 启用/禁用按钮
            const hasPath = state.hasPath;
            document.getElementById('playBtn').disabled = !hasPath;
            document.getElementById('stopBtn').disabled = !hasPath;
            ['seek0', 'seek25', 'seek50', 'seek75', 'seek100'].forEach(id => {
                document.getElementById(id).disabled = !hasPath;
            });
        }
        
        // 更新动画数据
        function updateAnimationData(animationState) {
            if (animationState.lookAt) {
                document.getElementById('lookAtLat').textContent = animationState.lookAt.lat.toFixed(6) + '°';
                document.getElementById('lookAtLon').textContent = animationState.lookAt.lon.toFixed(6) + '°';
                document.getElementById('lookAtAlt').textContent = animationState.lookAt.alt.toFixed(1) + 'm';
            }
        }
        
        // 加载GPX数据
        window.loadGPX = async function() {
            try {
                updateStatus('正在加载GPX数据...', 'info');
                
                // 创建控制器
                controller = new CameraAnimationController({
                    duration: 30000, // 30秒
                    onUpdate: (animationState) => {
                        updateAnimationData(animationState);
                        updateUI(controller.getState());
                    },
                    onComplete: () => {
                        updateStatus('动画播放完成', 'success');
                        updateUI(controller.getState());
                    }
                });
                
                // 加载GPX文件
                const result = await controller.initializeFromGPXURL('./workout_track.gpx');
                
                updateStatus(`✅ 成功加载 ${result.pointCount} 个轨迹点`, 'success');
                document.getElementById('pointCount').textContent = result.pointCount;
                
                // 更新UI状态
                updateUI(controller.getState());
                
                // 禁用加载按钮
                document.getElementById('loadBtn').disabled = true;
                
            } catch (error) {
                console.error('加载失败:', error);
                updateStatus(`❌ 加载失败: ${error.message}`, 'error');
            }
        };
        
        // 播放/暂停
        window.togglePlay = function() {
            if (!controller) return;
            
            const state = controller.getState();
            if (state.isPlaying) {
                controller.pause();
                updateStatus('动画已暂停', 'info');
            } else {
                controller.play();
                updateStatus('动画播放中...', 'info');
            }
        };
        
        // 停止
        window.stop = function() {
            if (!controller) return;
            
            controller.stop();
            updateStatus('动画已停止', 'info');
            updateUI(controller.getState());
        };
        
        // 跳转
        window.seekTo = function(progress) {
            if (!controller) return;
            
            controller.seekToProgress(progress);
            updateStatus(`跳转到 ${(progress * 100).toFixed(0)}%`, 'info');
        };
        
        // 初始化
        updateStatus('newCamera 库已加载，点击"加载GPX数据"开始', 'info');
        updateUI({ hasPath: false, isPlaying: false, progress: 0, currentTime: 0, duration: 30000 });
    </script>
</body>
</html>
